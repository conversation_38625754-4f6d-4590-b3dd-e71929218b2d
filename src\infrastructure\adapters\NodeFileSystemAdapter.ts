import { FileSystemPort, FileStats } from '../../application/ports/FileSystemPort.js';
import { readFile, readdir, stat, access } from 'fs/promises';
import { join, extname } from 'path';
import { constants } from 'fs';

export class NodeFileSystemAdapter implements FileSystemPort {
  async readFile(filePath: string): Promise<string> {
    try {
      return await readFile(filePath, 'utf-8');
    } catch (error) {
      throw new Error(`Impossible de lire le fichier ${filePath}: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }

  async listFiles(directoryPath: string, extensions: string[]): Promise<string[]> {
    const files: string[] = [];
    
    try {
      await this.collectFiles(directoryPath, extensions, files);
    } catch (error) {
      throw new Error(`Impossible de lister les fichiers dans ${directoryPath}: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
    
    return files;
  }

  private async collectFiles(dirPath: string, extensions: string[], files: string[]): Promise<void> {
    try {
      const entries = await readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          // Ignorer certains dossiers
          if (!['node_modules', '.git', 'dist', 'build'].includes(entry.name)) {
            await this.collectFiles(fullPath, extensions, files);
          }
        } else if (entry.isFile()) {
          const ext = extname(entry.name);
          if (extensions.includes(ext)) {
            files.push(fullPath);
          }
        }
      }
    } catch (error) {
      // Ignorer les erreurs de permission et continuer
      console.warn(`Impossible d'accéder au répertoire ${dirPath}:`, error);
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      await access(filePath, constants.F_OK);
      return true;
    } catch {
      return false;
    }
  }

  async getFileStats(filePath: string): Promise<FileStats> {
    try {
      const stats = await stat(filePath);
      return {
        size: stats.size,
        lastModified: stats.mtime,
        isDirectory: stats.isDirectory()
      };
    } catch (error) {
      throw new Error(`Impossible d'obtenir les statistiques du fichier ${filePath}: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  }
}
