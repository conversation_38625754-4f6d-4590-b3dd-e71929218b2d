import { CodeAnalyzerPort } from '../../application/ports/CodeAnalyzerPort.js';
import { EarsCriterion } from '../../domain/entities/EarsCriterion.js';
import { CodeEvidence } from '../../domain/entities/CodeEvidence.js';
import { ComplianceIssue } from '../../domain/entities/ComplianceIssue.js';
import { ComplianceReport } from '../../domain/entities/ComplianceReport.js';
import { FileSystemPort } from '../../application/ports/FileSystemPort.js';
import { Project, SourceFile, Node } from 'ts-morph';

export class TypeScriptAnalyzer implements CodeAnalyzerPort {
  constructor(private fileSystem: FileSystemPort) {}

  async analyzeCodebase(rootPath: string): Promise<CodeEvidence[]> {
    const evidence: CodeEvidence[] = [];
    
    try {
      // Obtenir tous les fichiers TypeScript
      const tsFiles = await this.fileSystem.listFiles(rootPath, ['.ts', '.tsx']);
      
      // Initialiser ts-morph project
      const project = new Project({
        tsConfigFilePath: 'tsconfig.json',
        skipAddingFilesFromTsConfig: true
      });
      
      // Ajouter les fichiers au projet
      for (const filePath of tsFiles) {
        try {
          const content = await this.fileSystem.readFile(filePath);
          project.createSourceFile(filePath, content, { overwrite: true });
        } catch (error) {
          console.warn(`Impossible de lire le fichier ${filePath}:`, error);
        }
      }
      
      // Analyser chaque fichier source
      for (const sourceFile of project.getSourceFiles()) {
        evidence.push(...this.extractEvidenceFromFile(sourceFile));
      }
      
    } catch (error) {
      console.error('Erreur lors de l\'analyse du code:', error);
    }
    
    return evidence;
  }

  private extractEvidenceFromFile(sourceFile: SourceFile): CodeEvidence[] {
    const evidence: CodeEvidence[] = [];
    const filePath = sourceFile.getFilePath();
    
    // Extraire les fonctions
    sourceFile.getFunctions().forEach((func: any) => {
      evidence.push({
        filePath,
        symbolName: func.getName() || 'anonymous',
        symbolType: 'function',
        lineNumber: func.getStartLineNumber(),
        codeSnippet: func.getText().substring(0, 200) + '...',
        confidence: this.calculateConfidence(func.getName() || '', func.getText())
      });
    });
    
    // Extraire les classes
    sourceFile.getClasses().forEach((cls: any) => {
      evidence.push({
        filePath,
        symbolName: cls.getName() || 'anonymous',
        symbolType: 'class',
        lineNumber: cls.getStartLineNumber(),
        codeSnippet: cls.getText().substring(0, 200) + '...',
        confidence: this.calculateConfidence(cls.getName() || '', cls.getText())
      });
      
      // Extraire les méthodes de classe
      cls.getMethods().forEach((method: any) => {
        evidence.push({
          filePath,
          symbolName: `${cls.getName()}.${method.getName()}`,
          symbolType: 'method',
          lineNumber: method.getStartLineNumber(),
          codeSnippet: method.getText().substring(0, 200) + '...',
          confidence: this.calculateConfidence(method.getName(), method.getText())
        });
      });
    });
    
    // Extraire les interfaces
    sourceFile.getInterfaces().forEach((iface: any) => {
      evidence.push({
        filePath,
        symbolName: iface.getName(),
        symbolType: 'interface',
        lineNumber: iface.getStartLineNumber(),
        codeSnippet: iface.getText().substring(0, 200) + '...',
        confidence: this.calculateConfidence(iface.getName(), iface.getText())
      });
    });
    
    return evidence;
  }

  private calculateConfidence(symbolName: string, codeText: string): number {
    let confidence = 0.1; // Base confidence
    
    // Mots-clés qui augmentent la confiance
    const keywords = ['validate', 'check', 'verify', 'ensure', 'handle', 'process', 'execute', 'perform'];
    const lowerSymbolName = symbolName.toLowerCase();
    const lowerCodeText = codeText.toLowerCase();
    
    keywords.forEach(keyword => {
      if (lowerSymbolName.includes(keyword)) confidence += 0.2;
      if (lowerCodeText.includes(keyword)) confidence += 0.1;
    });
    
    // Patterns spécifiques
    if (lowerCodeText.includes('throw') || lowerCodeText.includes('error')) confidence += 0.1;
    if (lowerCodeText.includes('return') || lowerCodeText.includes('response')) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }

  async findEvidenceForCriteria(criteria: EarsCriterion[], codeEvidence: CodeEvidence[]): Promise<ComplianceIssue[]> {
    const issues: ComplianceIssue[] = [];
    
    for (const criterion of criteria) {
      const matchingEvidence = this.findMatchingEvidence(criterion, codeEvidence);
      
      if (matchingEvidence.length === 0) {
        issues.push({
          criterionId: criterion.id,
          severity: 'MAJOR',
          issueType: 'MISSING_IMPLEMENTATION',
          message: `Aucune implémentation trouvée pour le critère: ${criterion.trigger} -> ${criterion.response}`,
          expectedEvidence: `Code implémentant: ${criterion.response}`,
          actualEvidence: [],
          suggestions: [
            `Implémenter une fonction ou méthode pour gérer: ${criterion.trigger}`,
            `Ajouter la logique pour: ${criterion.response}`
          ]
        });
      } else if (matchingEvidence.every(e => e.confidence < 0.5)) {
        issues.push({
          criterionId: criterion.id,
          severity: 'MINOR',
          issueType: 'PARTIAL_IMPLEMENTATION',
          message: `Implémentation partielle ou incertaine pour: ${criterion.trigger} -> ${criterion.response}`,
          expectedEvidence: `Code robuste implémentant: ${criterion.response}`,
          actualEvidence: matchingEvidence,
          suggestions: [
            'Améliorer la clarté du code et des noms de fonctions',
            'Ajouter des commentaires explicites sur l\'implémentation du critère'
          ]
        });
      }
    }
    
    return issues;
  }

  private findMatchingEvidence(criterion: EarsCriterion, codeEvidence: CodeEvidence[]): CodeEvidence[] {
    const triggerKeywords = this.extractKeywords(criterion.trigger);
    const responseKeywords = this.extractKeywords(criterion.response);
    
    return codeEvidence.filter(evidence => {
      const symbolNameLower = evidence.symbolName.toLowerCase();
      const codeSnippetLower = evidence.codeSnippet.toLowerCase();
      
      const triggerMatch = triggerKeywords.some(keyword => 
        symbolNameLower.includes(keyword) || codeSnippetLower.includes(keyword)
      );
      
      const responseMatch = responseKeywords.some(keyword => 
        symbolNameLower.includes(keyword) || codeSnippetLower.includes(keyword)
      );
      
      return triggerMatch || responseMatch;
    });
  }

  private extractKeywords(text: string): string[] {
    // Extraire les mots-clés significatifs du texte
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    // Filtrer les mots communs
    const commonWords = ['quand', 'alors', 'doit', 'système', 'application', 'serveur', 'utilisateur'];
    return words.filter(word => !commonWords.includes(word));
  }

  async generateComplianceReport(criteria: EarsCriterion[], issues: ComplianceIssue[]): Promise<ComplianceReport> {
    const totalCriteria = criteria.length;
    const criticalIssues = issues.filter(i => i.severity === 'CRITICAL').length;
    const majorIssues = issues.filter(i => i.severity === 'MAJOR').length;
    const minorIssues = issues.filter(i => i.severity === 'MINOR').length;
    
    const implementedCriteria = totalCriteria - issues.filter(i => i.issueType === 'MISSING_IMPLEMENTATION').length;
    const coveragePercentage = totalCriteria > 0 ? (implementedCriteria / totalCriteria) * 100 : 0;
    
    let overallStatus: 'PASS' | 'NEEDS_IMPROVEMENT' | 'MAJOR_ISSUES';
    if (criticalIssues > 0 || majorIssues > totalCriteria * 0.3) {
      overallStatus = 'MAJOR_ISSUES';
    } else if (majorIssues > 0 || minorIssues > totalCriteria * 0.5) {
      overallStatus = 'NEEDS_IMPROVEMENT';
    } else {
      overallStatus = 'PASS';
    }
    
    const suggestions: string[] = [];
    const strengths: string[] = [];
    
    if (coveragePercentage >= 80) {
      strengths.push('Bonne couverture des critères EARS');
    }
    if (criticalIssues === 0) {
      strengths.push('Aucun problème critique détecté');
    }
    
    if (majorIssues > 0) {
      suggestions.push('Implémenter les fonctionnalités manquantes identifiées');
    }
    if (minorIssues > 0) {
      suggestions.push('Améliorer la clarté et la documentation du code');
    }
    if (coveragePercentage < 70) {
      suggestions.push('Augmenter la couverture d\'implémentation des critères EARS');
    }
    
    return {
      overallStatus,
      totalCriteria,
      implementedCriteria,
      issues,
      suggestions,
      strengths,
      coveragePercentage,
      generatedAt: new Date()
    };
  }
}
