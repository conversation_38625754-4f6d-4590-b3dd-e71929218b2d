import { readFile, access } from 'fs/promises';
import { join } from 'path';
import { TemplateProviderPort } from '../../application/ports/TemplateProviderPort.js';

/**
 * Adaptateur qui implémente TemplateProviderPort en utilisant le système de fichiers.
 * Lit les fichiers modèles du dossier .claude/templates/
 */
export class FileSystemTemplateProvider implements TemplateProviderPort {
  private readonly templatesPath: string;

  constructor(templatesPath: string = '.claude/templates') {
    this.templatesPath = templatesPath;
  }

  /**
   * Charge un modèle par son nom
   * @param templateName Le nom du modèle (ex: "requirements-template", "design-template")
   * @returns Le contenu du modèle
   */
  async getTemplate(templateName: string): Promise<string> {
    try {
      const templatePath = join(this.templatesPath, `${templateName}.md`);
      const content = await readFile(templatePath, 'utf-8');
      return content;
    } catch (error) {
      throw new Error(`Impossible de charger le modèle "${templateName}": ${error}`);
    }
  }

  /**
   * Vérifie si un modèle existe
   * @param templateName Le nom du modèle
   * @returns True si le modèle existe, false sinon
   */
  async templateExists(templateName: string): Promise<boolean> {
    try {
      const templatePath = join(this.templatesPath, `${templateName}.md`);
      await access(templatePath);
      return true;
    } catch {
      return false;
    }
  }
}
