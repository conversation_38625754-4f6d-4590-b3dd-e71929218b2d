# Commande de création de bug

Initialisez un nouveau workflow de correction de bug pour suivre et résoudre les bugs.

## Utilisation
```
/bug-create <nom-du-bug> [description]
```

## Vue d'ensemble du workflow

Il s'agit du **workflow de correction de bug simplifié** – une alternative légère au workflow complet pour traiter les bugs et problèmes.

### Phases de correction de bug
1. **Phase de rapport** (cette commande) – Documenter le bug
2. **Phase d'analyse** (`/bug-analyze`) – Enquêter sur la cause racine
3. **Phase de correction** (`/bug-fix`) – Implémenter la solution
4. **Phase de vérification** (`/bug-verify`) – Confirmer la résolution

## Instructions

Vous aidez à créer un nouveau workflow de correction de bug. Il est conçu pour les corrections mineures qui ne nécessitent pas la lourdeur du workflow complet.

1. **Créer la structure de répertoires**
   - C<PERSON>ez le dossier `.sdd/bugs/{nom-du-bug}/`
   - Initialisez les fichiers report.md, analysis.md et verification.md

2. **Charger TOUT le contexte une seule fois (Chargement contextuel hiérarchique)**
   Chargez le contexte complet au début du processus de création du bug :

   ```bash
   # Charger les documents de pilotage (si disponibles)
   claude-code-spec-workflow get-steering-context

   # Charger les modèles de bug
   claude-code-spec-workflow get-template-context bug
   ```

3. **Recueillir les informations sur le bug**
   - Prendre le nom du bug et la description optionnelle
   - Guider l'utilisateur dans la création du rapport de bug
   - Utiliser un format structuré pour la cohérence

4. **Générer le rapport de bug**
   - **Modèle à suivre** : Utilisez le modèle de rapport de bug du contexte préchargé ci-dessus (ne pas recharger)
   - Créez une description détaillée du bug en suivant la structure du modèle de rapport de bug

## Utilisation du modèle
- **Suivre la structure exacte** : Utilisez précisément le modèle de rapport de bug chargé
- **Inclure toutes les sections** : N'omettez aucune section requise du modèle
- **Format structuré** : Respectez le format du modèle pour la cohérence

5. **Demander des informations à l'utilisateur**
   - Demandez les détails du bug si non fournis dans la description
   - Guidez à travers chaque section du rapport de bug
   - Assurez-vous que toutes les informations requises sont capturées

6. **Enregistrer et poursuivre**
   - Enregistrez le rapport de bug complété dans report.md
   - Demandez : "Ce rapport de bug est-il exact ? Si oui, nous pouvons passer à l'analyse."
   - Attendez une approbation explicite avant de poursuivre

## Principales différences avec le workflow de spécification

- **Plus rapide** : Pas de phases exigences/conception
- **Ciblé** : Se concentre sur la correction de la fonctionnalité existante
- **Simplifié** : 4 phases au lieu d'un workflow détaillé
- **Pratique** : Directement du problème à la solution

## Règles

- Ne créez qu'UN seul correctif de bug à la fois
- Utilisez toujours le kebab-case pour les noms de bug
- Analysez obligatoirement la base de code existante lors de l'investigation
- Respectez les conventions et modèles du projet existant
- Ne poursuivez pas sans l'approbation de l'utilisateur entre les phases

## Gestion des erreurs

Si des problèmes surviennent pendant le workflow :
- **Bug peu clair** : Posez des questions ciblées pour clarifier
- **Trop complexe** : Suggérez de diviser en bugs plus petits ou d'utiliser le workflow de spécification
- **Blocage de reproduction** : Documentez les blocages et proposez des alternatives

## Exemple
```
/bug-create login-timeout "Les utilisateurs sont déconnectés trop rapidement"
```

## Étapes suivantes
Après l'approbation du rapport de bug, passez à la phase `/bug-analyze`.
