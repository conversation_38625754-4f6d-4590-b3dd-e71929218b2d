I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current codebase has a clean hexagonal architecture with domain entities, application use cases, infrastructure adapters, and presentation layer. The existing use cases show that specs are stored in a `specs/` folder with `requirements.md` and `design.md` files. The validation agents provide detailed criteria for document validation. The user wants to extend this with a validation system that compares specifications with generated code using EARS criteria parsing and static code analysis.

### Approach

I'll extend the existing hexagonal architecture by adding new domain entities for EARS criteria and compliance reports, creating parsers for extracting EARS patterns from French markdown documents, implementing a TypeScript static analyzer, and enhancing the existing validation system. The solution will leverage the existing `ValidateSpecUseCase` pattern and integrate seamlessly with the current MCP server structure. The implementation will use regex patterns for EARS parsing and `ts-morph` for TypeScript code analysis, providing comprehensive compliance reports with actionable suggestions.

### Reasoning

I explored the repository structure and read the key implementation files including use cases, domain entities, infrastructure adapters, and presentation layer. I analyzed the French templates to understand the EARS format patterns (QUAND/SI/ALORS) and examined the validation agents to understand the quality criteria. I confirmed the file storage conventions (`specs/` folder) and understood the current MCP prompt registration patterns in the server factory.

## Mermaid Diagram

sequenceDiagram
    participant Client as MCP Client
    participant Presentation as Presentation Layer<br/>(McpServerFactory)
    participant UseCase as ValidateSpecComplianceUseCase
    participant SpecParser as MarkdownEarsParser<br/>(SpecParserPort)
    participant CodeAnalyzer as TypeScriptAnalyzer<br/>(CodeAnalyzerPort)
    participant FileSystem as NodeFileSystemAdapter<br/>(FileSystemPort)
    participant Specs as specs/ directory
    participant Code as src/ directory

    Client->>Presentation: MCP Prompt Request<br/>(spec-validate)
    Presentation->>UseCase: Execute validation workflow
    
    UseCase->>FileSystem: Check specs/requirements.md exists
    FileSystem->>Specs: Read requirements.md
    Specs-->>FileSystem: Document content
    FileSystem-->>UseCase: File content
    
    UseCase->>SpecParser: Parse EARS criteria<br/>from requirements
    SpecParser->>SpecParser: Extract QUAND/SI/ALORS patterns<br/>using regex
    SpecParser-->>UseCase: EarsCriterion[]
    
    UseCase->>CodeAnalyzer: Analyze codebase<br/>for evidence
    CodeAnalyzer->>FileSystem: List TypeScript files
    FileSystem->>Code: Scan src/ directory
    Code-->>FileSystem: File paths
    FileSystem-->>CodeAnalyzer: TypeScript files
    CodeAnalyzer->>CodeAnalyzer: Parse with ts-morph<br/>extract symbols
    CodeAnalyzer-->>UseCase: CodeEvidence[]
    
    UseCase->>UseCase: Match criteria with evidence<br/>generate ComplianceIssues
    UseCase->>UseCase: Create ComplianceReport<br/>with suggestions
    
    UseCase-->>Presentation: ComplianceReport
    Presentation->>Presentation: Format as ChatMessage<br/>with compliance status
    Presentation-->>Client: MCP Response with<br/>validation results + suggestions

    Note over SpecParser: Parses French EARS patterns:<br/>QUAND [event] ALORS [response]<br/>SI [condition] ALORS [response]
    Note over CodeAnalyzer: Analyzes TypeScript AST<br/>for implementation evidence<br/>using semantic matching

## Proposed File Changes

### src\domain\entities\EarsCriterion.ts(NEW)

References: 

- .claude\templates\requirements-template.md

Create EarsCriterion entity to represent parsed EARS criteria from requirements documents. Include properties: `id` (unique identifier), `type` (QUAND/SI/ALORS pattern type), `trigger` (the event or condition), `response` (the expected system behavior), `context` (additional conditions), `sourceSection` (which requirement section it came from), and `lineNumber` (for traceability). This entity encapsulates the core business concept of an EARS criterion independent of parsing implementation.

### src\domain\entities\CodeEvidence.ts(NEW)

Create CodeEvidence entity to represent evidence found in code that relates to EARS criteria. Include properties: `filePath` (relative path to the code file), `symbolName` (function/class/variable name), `symbolType` (function, class, interface, etc.), `lineNumber` (location in file), `codeSnippet` (relevant code excerpt), and `confidence` (how strongly this evidence supports the criterion). This entity represents the connection between specifications and implementation.

### src\domain\entities\ComplianceIssue.ts(NEW)

References: 

- src\domain\entities\EarsCriterion.ts(NEW)
- src\domain\entities\CodeEvidence.ts(NEW)

Create ComplianceIssue entity to represent validation problems found during spec-code comparison. Include properties: `criterionId` (reference to the EARS criterion), `severity` (CRITICAL, MAJOR, MINOR), `issueType` (MISSING_IMPLEMENTATION, PARTIAL_IMPLEMENTATION, INCORRECT_IMPLEMENTATION), `message` (human-readable description), `expectedEvidence` (what should be found in code), `actualEvidence` (what was actually found), and `suggestions` (array of improvement recommendations). This entity encapsulates validation findings.

### src\domain\entities\ComplianceReport.ts(NEW)

References: 

- src\domain\entities\ComplianceIssue.ts(NEW)

Create ComplianceReport entity to represent the overall validation results. Include properties: `overallStatus` (PASS, NEEDS_IMPROVEMENT, MAJOR_ISSUES), `totalCriteria` (number of EARS criteria analyzed), `implementedCriteria` (number with evidence), `issues` (array of ComplianceIssue), `suggestions` (general improvement recommendations), `strengths` (well-implemented aspects), `coveragePercentage` (ratio of implemented vs total criteria), and `generatedAt` (timestamp). This entity aggregates all validation findings into a comprehensive report.

### src\domain\entities\index.ts(MODIFY)

References: 

- src\domain\entities\EarsCriterion.ts(NEW)
- src\domain\entities\CodeEvidence.ts(NEW)
- src\domain\entities\ComplianceIssue.ts(NEW)
- src\domain\entities\ComplianceReport.ts(NEW)

Update the domain entities barrel export to include the new validation entities. Add exports for EarsCriterion, CodeEvidence, ComplianceIssue, and ComplianceReport alongside the existing ChatMessage and PromptDefinition exports. This maintains the clean domain layer interface while exposing the new validation domain concepts.

### src\application\ports\SpecParserPort.ts(NEW)

References: 

- src\domain\entities\EarsCriterion.ts(NEW)
- .claude\templates\requirements-template.md

Create SpecParserPort interface that defines the contract for parsing specification documents to extract EARS criteria. Include methods: `parseRequirements(filePath: string): Promise<EarsCriterion[]>` to extract EARS patterns from requirements.md, `parseDesign(filePath: string): Promise<DesignComponent[]>` to extract design components, and `validateDocumentStructure(filePath: string, templatePath: string): Promise<StructureValidation>` to check template compliance. This port will be implemented by infrastructure adapters that handle the actual file parsing logic.

### src\application\ports\CodeAnalyzerPort.ts(NEW)

References: 

- src\domain\entities\EarsCriterion.ts(NEW)
- src\domain\entities\CodeEvidence.ts(NEW)
- src\domain\entities\ComplianceIssue.ts(NEW)
- src\domain\entities\ComplianceReport.ts(NEW)

Create CodeAnalyzerPort interface that defines the contract for static code analysis. Include methods: `analyzeCodebase(rootPath: string): Promise<CodeEvidence[]>` to scan all code files and extract symbols, `findEvidenceForCriteria(criteria: EarsCriterion[], codeEvidence: CodeEvidence[]): Promise<ComplianceIssue[]>` to match EARS criteria with code evidence, and `generateComplianceReport(criteria: EarsCriterion[], issues: ComplianceIssue[]): Promise<ComplianceReport>` to create the final validation report. This port abstracts the static analysis implementation details.

### src\application\ports\FileSystemPort.ts(NEW)

Create FileSystemPort interface for file operations needed by the validation system. Include methods: `readFile(filePath: string): Promise<string>` for reading document content, `listFiles(directoryPath: string, extensions: string[]): Promise<string[]>` for discovering code files, `fileExists(filePath: string): Promise<boolean>` for checking file existence, and `getFileStats(filePath: string): Promise<FileStats>` for file metadata. This port decouples the application layer from Node.js file system APIs and enables easier testing.

### src\application\ports\index.ts(MODIFY)

References: 

- src\application\ports\SpecParserPort.ts(NEW)
- src\application\ports\CodeAnalyzerPort.ts(NEW)
- src\application\ports\FileSystemPort.ts(NEW)

Update the application ports barrel export to include the new validation ports. Add exports for SpecParserPort, CodeAnalyzerPort, and FileSystemPort alongside the existing PromptBuilderPort export. This maintains the clean application layer interface while exposing the new validation contracts.

### src\application\use-cases\ValidateSpecComplianceUseCase.ts(NEW)

References: 

- src\application\ports\SpecParserPort.ts(NEW)
- src\application\ports\CodeAnalyzerPort.ts(NEW)
- src\application\ports\FileSystemPort.ts(NEW)
- src\domain\entities\ComplianceReport.ts(NEW)

Create ValidateSpecComplianceUseCase that orchestrates the complete spec-to-code validation workflow. The use case will: 1) Use FileSystemPort to check if specs/requirements.md and specs/design.md exist, 2) Use SpecParserPort to extract EARS criteria from requirements, 3) Use CodeAnalyzerPort to scan the codebase and find evidence, 4) Match criteria with evidence to identify compliance issues, 5) Generate a comprehensive ComplianceReport with actionable suggestions. Include error handling for missing files and parsing failures. This use case encapsulates the core business logic for spec-code validation.

### src\application\use-cases\index.ts(MODIFY)

References: 

- src\application\use-cases\ValidateSpecComplianceUseCase.ts(NEW)

Update the application use cases barrel export to include the new ValidateSpecComplianceUseCase alongside the existing GenerateRequirementsUseCase, GenerateDesignFromRequirementsUseCase, and GenerateCodeFromDesignUseCase exports. This maintains the clean application layer interface while exposing the new validation use case.

### src\infrastructure\adapters\MarkdownEarsParser.ts(NEW)

References: 

- src\application\ports\SpecParserPort.ts(NEW)
- src\domain\entities\EarsCriterion.ts(NEW)
- .claude\templates\requirements-template.md

Create MarkdownEarsParser that implements SpecParserPort interface. This adapter will parse French markdown documents to extract EARS criteria using regex patterns. Implement logic to: 1) Read markdown content and identify sections, 2) Use regex patterns to match EARS formats like `QUAND [événement] ALORS [système] DOIT [réponse]` and `SI [précondition] ALORS [système] DOIT [réponse]`, 3) Extract trigger events, system responses, and conditions, 4) Generate unique IDs for each criterion, 5) Track source sections and line numbers for traceability. Include error handling for malformed EARS statements and provide detailed parsing feedback.

### src\infrastructure\adapters\TypeScriptAnalyzer.ts(NEW)

References: 

- src\application\ports\CodeAnalyzerPort.ts(NEW)
- src\domain\entities\CodeEvidence.ts(NEW)
- src\domain\entities\ComplianceReport.ts(NEW)

Create TypeScriptAnalyzer that implements CodeAnalyzerPort interface using ts-morph library for static analysis. This adapter will: 1) Scan TypeScript files in the src/ directory recursively, 2) Extract functions, classes, interfaces, and variables with their signatures, 3) Analyze function names, parameters, and return types for semantic matching with EARS criteria, 4) Look for keywords and patterns that indicate implementation of specific requirements (e.g., validation functions for 'DOIT' requirements), 5) Generate CodeEvidence objects with confidence scores based on naming conventions and code patterns, 6) Provide detailed code snippets and location information for traceability. Include support for common TypeScript patterns and frameworks.

### src\infrastructure\adapters\NodeFileSystemAdapter.ts(NEW)

References: 

- src\application\ports\FileSystemPort.ts(NEW)

Create NodeFileSystemAdapter that implements FileSystemPort interface using Node.js fs/promises APIs. This adapter will: 1) Provide async file reading with proper error handling for missing files, 2) Implement directory traversal with file extension filtering for code discovery, 3) Handle path resolution relative to the project root, 4) Include file existence checks and metadata retrieval, 5) Provide proper error messages for file system issues. Follow the same patterns as existing infrastructure adapters in the codebase for consistency.

### src\infrastructure\adapters\index.ts(MODIFY)

References: 

- src\infrastructure\adapters\MarkdownEarsParser.ts(NEW)
- src\infrastructure\adapters\TypeScriptAnalyzer.ts(NEW)
- src\infrastructure\adapters\NodeFileSystemAdapter.ts(NEW)

Create barrel export file for infrastructure adapters to export MarkdownEarsParser, TypeScriptAnalyzer, and NodeFileSystemAdapter. This provides a clean interface for the presentation layer to import and instantiate the validation adapters.

### src\infrastructure\index.ts(MODIFY)

References: 

- src\infrastructure\adapters\index.ts(MODIFY)

Update the infrastructure barrel export to include the new adapters directory alongside the existing mcp directory. Add export for the adapters module to maintain the clean infrastructure layer interface.

### src\presentation\mcp\McpServerFactory.ts(MODIFY)

References: 

- src\application\use-cases\ValidateSpecComplianceUseCase.ts(NEW)
- src\infrastructure\adapters\index.ts(MODIFY)
- src\domain\entities\ComplianceReport.ts(NEW)

Extend McpServerFactory to register the new spec-validate prompt that includes code compliance validation. Add imports for ValidateSpecComplianceUseCase and the new infrastructure adapters (MarkdownEarsParser, TypeScriptAnalyzer, NodeFileSystemAdapter). Instantiate these adapters and inject them into the ValidateSpecComplianceUseCase. Register a new MCP prompt `spec-validate` with schema for optional feature name parameter. The prompt handler will execute the use case and format the ComplianceReport as ChatMessage responses, maintaining the same response pattern as existing prompts. Follow the existing dependency injection pattern used for other use cases.

### package.json(MODIFY)

References: 

- src\infrastructure\adapters\TypeScriptAnalyzer.ts(NEW)

Add ts-morph as a dependency for TypeScript static analysis capabilities. Add the dependency `"ts-morph": "^20.0.0"` to the dependencies section. This library provides a TypeScript compiler wrapper that makes it easy to analyze and manipulate TypeScript code programmatically, which is essential for the static code analysis functionality.

### .claude\commands\spec-validate-compliance.md(NEW)

References: 

- .claude\agents\spec-requirements-validator.md
- .claude\templates\requirements-template.md

Create command specification for the enhanced `/spec-validate` command that includes code compliance validation. Document the command usage for validating consistency between EARS criteria in specifications and actual code implementation. Specify that this command: 1) Parses requirements.md to extract EARS criteria, 2) Analyzes TypeScript codebase for implementation evidence, 3) Generates compliance reports with coverage percentages, 4) Provides actionable suggestions for improving spec-code alignment. Include expected output format showing compliance status, missing implementations, and improvement recommendations. Reference the integration with existing validation agents and the new static analysis capabilities.