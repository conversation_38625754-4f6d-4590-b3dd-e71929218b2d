import { ValidationAgentPort } from '../../application/ports/ValidationAgentPort.js';
import { SpecDocument } from '../../domain/entities/SpecDocument.js';
import { ValidationReport } from '../../domain/entities/ValidationReport.js';

/**
 * Adaptateur qui implémente ValidationAgentPort en simulant le comportement
 * des agents de validation du dossier .claude/agents/
 */
export class McpValidationAdapter implements ValidationAgentPort {

  /**
   * Valide un document d'exigences
   * @param document Le document d'exigences à valider
   * @returns Le rapport de validation
   */
  async validateRequirements(document: SpecDocument): Promise<ValidationReport> {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const strengths: string[] = [];

    // Validation de la structure du document
    if (!document.content.includes('# Exigences') && !document.content.includes('# Requirements')) {
      issues.push('Le document doit contenir un titre principal "Exigences" ou "Requirements"');
    }

    // Validation du format EARS
    const earsPattern = /QUAND|LORSQUE|SI|ALORS|LE SYSTÈME DOIT|WHEN|IF|THEN|THE SYSTEM SHALL/i;
    if (!earsPattern.test(document.content)) {
      suggestions.push('Considérer l\'utilisation du format EARS pour structurer les exigences');
    } else {
      strengths.push('Utilisation appropriée du format EARS détectée');
    }

    // Validation de la longueur du contenu
    if (document.content.length < 100) {
      issues.push('Le document d\'exigences semble trop court pour être complet');
    } else if (document.content.length > 200) {
      strengths.push('Document d\'exigences bien détaillé');
    }

    // Validation de la présence de sections importantes
    if (!document.content.includes('Objectif') && !document.content.includes('Objective')) {
      suggestions.push('Ajouter une section décrivant l\'objectif principal');
    }

    // Déterminer le statut
    let status: "PASS" | "BESOIN_AMÉLIORATION" | "PROBLÈMES_MAJEURS";
    if (issues.length > 2) {
      status = "PROBLÈMES_MAJEURS";
    } else if (issues.length > 0 || suggestions.length > 2) {
      status = "BESOIN_AMÉLIORATION";
    } else {
      status = "PASS";
    }

    return {
      status,
      issues,
      suggestions,
      strengths
    };
  }

  /**
   * Valide un document de conception
   * @param document Le document de conception à valider
   * @returns Le rapport de validation
   */
  async validateDesign(document: SpecDocument): Promise<ValidationReport> {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const strengths: string[] = [];

    // Validation de la structure du document
    if (!document.content.includes('# Conception') && !document.content.includes('# Design')) {
      issues.push('Le document doit contenir un titre principal "Conception" ou "Design"');
    }

    // Validation de la présence d'architecture
    if (!document.content.includes('Architecture') && !document.content.includes('architecture')) {
      suggestions.push('Ajouter une section décrivant l\'architecture du système');
    } else {
      strengths.push('Section architecture présente');
    }

    // Validation de la présence de composants
    if (!document.content.includes('Composant') && !document.content.includes('Component')) {
      suggestions.push('Décrire les composants principaux du système');
    } else {
      strengths.push('Composants du système décrits');
    }

    // Validation de la longueur du contenu
    if (document.content.length < 150) {
      issues.push('Le document de conception semble incomplet');
    } else if (document.content.length > 300) {
      strengths.push('Document de conception détaillé');
    }

    // Déterminer le statut
    let status: "PASS" | "BESOIN_AMÉLIORATION" | "PROBLÈMES_MAJEURS";
    if (issues.length > 2) {
      status = "PROBLÈMES_MAJEURS";
    } else if (issues.length > 0 || suggestions.length > 2) {
      status = "BESOIN_AMÉLIORATION";
    } else {
      status = "PASS";
    }

    return {
      status,
      issues,
      suggestions,
      strengths
    };
  }

  /**
   * Effectue une validation croisée entre les spécifications et le code
   * @param requirementsDoc Le document d'exigences
   * @param designDoc Le document de conception
   * @param codeFiles Les fichiers de code existants
   * @returns Le rapport de validation croisée
   */
  async validateCrossConsistency(
    requirementsDoc: SpecDocument,
    designDoc: SpecDocument,
    codeFiles: string[]
  ): Promise<ValidationReport> {
    const issues: string[] = [];
    const suggestions: string[] = [];
    const strengths: string[] = [];

    // Validation de la cohérence entre exigences et conception
    const requirementsFeatures = this.extractFeatures(requirementsDoc.content);
    const designFeatures = this.extractFeatures(designDoc.content);
    
    const missingInDesign = requirementsFeatures.filter(f => !designFeatures.includes(f));
    if (missingInDesign.length > 0) {
      issues.push(`Fonctionnalités manquantes dans la conception: ${missingInDesign.join(', ')}`);
    }

    // Validation de la présence de code
    if (codeFiles.length === 0) {
      suggestions.push('Aucun fichier de code trouvé pour validation croisée');
    } else {
      strengths.push(`${codeFiles.length} fichiers de code analysés`);
      
      // Validation basique de la cohérence code/conception
      const hasMainComponents = codeFiles.some(file => 
        file.includes('component') || file.includes('service') || file.includes('model')
      );
      
      if (!hasMainComponents) {
        suggestions.push('Vérifier que les composants principaux sont implémentés');
      } else {
        strengths.push('Structure de code cohérente avec la conception');
      }
    }

    // Déterminer le statut
    let status: "PASS" | "BESOIN_AMÉLIORATION" | "PROBLÈMES_MAJEURS";
    if (issues.length > 1) {
      status = "PROBLÈMES_MAJEURS";
    } else if (issues.length > 0 || suggestions.length > 1) {
      status = "BESOIN_AMÉLIORATION";
    } else {
      status = "PASS";
    }

    return {
      status,
      issues,
      suggestions,
      strengths
    };
  }

  /**
   * Extrait les fonctionnalités mentionnées dans un document
   */
  private extractFeatures(content: string): string[] {
    const features: string[] = [];
    const lines = content.split('\n');
    
    for (const line of lines) {
      // Recherche de patterns indiquant des fonctionnalités
      if (line.includes('fonctionnalité') || line.includes('feature') || 
          line.includes('doit') || line.includes('shall') ||
          line.includes('peut') || line.includes('may')) {
        const feature = line.trim().toLowerCase();
        if (feature.length > 10 && feature.length < 100) {
          features.push(feature);
        }
      }
    }
    
    return [...new Set(features)]; // Supprimer les doublons
  }
}
