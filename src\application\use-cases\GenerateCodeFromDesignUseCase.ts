import { ChatMessage } from '../../domain/entities/ChatMessage.js';
import { PromptBuilderPort } from '../ports/PromptBuilderPort.js';

/**
 * Cas d'utilisation pour générer du code à partir des documents de conception.
 * Encapsule la logique métier pour le prompt generate-code-from-design.
 */
export class GenerateCodeFromDesignUseCase {
  constructor(private promptBuilder: PromptBuilderPort) {}

  /**
   * Exécute le cas d'utilisation pour générer du code
   * @returns La réponse formatée pour MCP
   */
  execute(): { messages: any[] } {
    const chatMessages: ChatMessage[] = [{
      role: "user",
      content: {
        type: "text",
        text: `Based on specs/design.md, generate code on the root folder`
      }
    }];

    return this.promptBuilder.buildPromptResponse(chatMessages);
  }
}
