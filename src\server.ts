import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { McpServerFactory } from "./presentation/mcp/McpServerFactory.js";

/**
 * Fonction createServer refactorisée pour utiliser l'architecture hexagonale.
 * Délègue la création du serveur à McpServerFactory tout en maintenant
 * la même signature publique pour la compatibilité.
 */
export function createServer(): McpServer {
  return McpServerFactory.createServer();
}
