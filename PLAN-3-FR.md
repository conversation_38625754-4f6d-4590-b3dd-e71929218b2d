J'ai établi le plan suivant après une exploration et une analyse approfondies de la base de code. Suivez ce plan à la lettre. Faites confiance aux fichiers et aux références. Ne revérifiez pas ce qui est écrit dans le plan. Explorez uniquement si absolument nécessaire. Implémentez d'abord tous les changements de fichiers proposés, puis je passerai en revue toutes les modifications ensemble à la fin.

### Observations

La base de code actuelle adopte une architecture hexagonale propre avec des entités du domaine, des cas d'utilisation applicatifs, des adaptateurs d'infrastructure et une couche de présentation. Les cas d'utilisation existants montrent que les spécifications sont stockées dans un dossier `specs/` avec les fichiers `requirements.md` et `design.md`. Les agents de validation fournissent des critères détaillés pour la validation des documents. L'utilisateur souhaite étendre cela avec un système de validation qui compare les spécifications au code généré en utilisant l'analyse des critères EARS et l'analyse statique du code.

### Approche

Je vais étendre l'architecture hexagonale existante en ajoutant de nouvelles entités de domaine pour les critères EARS et les rapports de conformité, en créant des parseurs pour extraire les motifs EARS des documents markdown en français, en implémentant un analyseur statique TypeScript, et en améliorant le système de validation existant. La solution s'appuiera sur le modèle de cas d'utilisation `ValidateSpecUseCase` et s'intégrera parfaitement à la structure actuelle du serveur MCP. L'implémentation utilisera des expressions régulières pour le parsing EARS et `ts-morph` pour l'analyse du code TypeScript, fournissant des rapports de conformité complets avec des suggestions actionnables.

### Raisonnement

J'ai exploré la structure du dépôt et lu les fichiers clés d'implémentation, y compris les cas d'utilisation, les entités du domaine, les adaptateurs d'infrastructure et la couche de présentation. J'ai analysé les templates français pour comprendre les motifs du format EARS (QUAND/SI/ALORS) et examiné les agents de validation pour comprendre les critères de qualité. J'ai confirmé les conventions de stockage des fichiers (dossier `specs/`) et compris les modèles actuels d'enregistrement des prompts MCP dans la fabrique du serveur.

## Diagramme Mermaid

sequenceDiagram
    participant Client as Client MCP
    participant Presentation as Couche Présentation<br/>(McpServerFactory)
    participant UseCase as Cas d'utilisation ValidateSpecComplianceUseCase
    participant SpecParser as MarkdownEarsParser<br/>(SpecParserPort)
    participant CodeAnalyzer as TypeScriptAnalyzer<br/>(CodeAnalyzerPort)
    participant FileSystem as NodeFileSystemAdapter<br/>(FileSystemPort)
    participant Specs as dossier specs/
    participant Code as dossier src/

    Client->>Presentation: Requête MCP Prompt<br/>(spec-validate)
    Presentation->>UseCase: Exécute le workflow de validation
    
    UseCase->>FileSystem: Vérifie l'existence de specs/requirements.md
    FileSystem->>Specs: Lit requirements.md
    Specs-->>FileSystem: Contenu du document
    FileSystem-->>UseCase: Contenu du fichier
    
    UseCase->>SpecParser: Parse les critères EARS<br/>depuis requirements
    SpecParser->>SpecParser: Extrait les motifs QUAND/SI/ALORS<br/>avec regex
    SpecParser-->>UseCase: EarsCriterion[]
    
    UseCase->>CodeAnalyzer: Analyse la base de code<br/>pour trouver des preuves
    CodeAnalyzer->>FileSystem: Liste les fichiers TypeScript
    FileSystem->>Code: Parcourt le dossier src/
    Code-->>FileSystem: Chemins des fichiers
    FileSystem-->>CodeAnalyzer: Fichiers TypeScript
    CodeAnalyzer->>CodeAnalyzer: Parse avec ts-morph<br/>extrait les symboles
    CodeAnalyzer-->>UseCase: CodeEvidence[]
    
    UseCase->>UseCase: Associe critères et preuves<br/>génère ComplianceIssues
    UseCase->>UseCase: Crée un ComplianceReport<br/>avec suggestions
    
    UseCase-->>Presentation: ComplianceReport
    Presentation->>Presentation: Formate en ChatMessage<br/>avec statut de conformité
    Presentation-->>Client: Réponse MCP avec<br/>résultats de validation + suggestions

    Note over SpecParser: Parse les motifs EARS français :<br/>QUAND [événement] ALORS [réponse]<br/>SI [condition] ALORS [réponse]
    Note over CodeAnalyzer: Analyse l'AST TypeScript<br/>pour trouver des preuves d'implémentation<br/>avec correspondance sémantique

## Modifications de fichiers proposées

### src\domain\entities\EarsCriterion.ts(NOUVEAU)

Références : 

- .claude\templates\requirements-template.md

Créer l'entité EarsCriterion pour représenter les critères EARS extraits des documents de requirements. Inclure les propriétés : `id` (identifiant unique), `type` (type de motif QUAND/SI/ALORS), `trigger` (événement ou condition), `response` (comportement attendu du système), `context` (conditions additionnelles), `sourceSection` (section du requirement d'origine), et `lineNumber` (pour la traçabilité). Cette entité encapsule le concept métier central d'un critère EARS indépendamment de l'implémentation du parsing.

### src\domain\entities\CodeEvidence.ts(NOUVEAU)

Créer l'entité CodeEvidence pour représenter les preuves trouvées dans le code en lien avec les critères EARS. Inclure les propriétés : `filePath` (chemin relatif du fichier de code), `symbolName` (nom de la fonction/classe/variable), `symbolType` (fonction, classe, interface, etc.), `lineNumber` (emplacement dans le fichier), `codeSnippet` (extrait de code pertinent), et `confidence` (niveau de confiance dans la correspondance avec le critère). Cette entité représente le lien entre spécifications et implémentation.

### src\domain\entities\ComplianceIssue.ts(NOUVEAU)

Références : 

- src\domain\entities\EarsCriterion.ts(NOUVEAU)
- src\domain\entities\CodeEvidence.ts(NOUVEAU)

Créer l'entité ComplianceIssue pour représenter les problèmes de validation identifiés lors de la comparaison spec-code. Inclure les propriétés : `criterionId` (référence au critère EARS), `severity` (CRITIQUE, MAJEUR, MINEUR), `issueType` (IMPLEMENTATION_MANQUANTE, IMPLEMENTATION_PARTIELLE, IMPLEMENTATION_INCORRECTE), `message` (description lisible), `expectedEvidence` (ce qui devrait être trouvé dans le code), `actualEvidence` (ce qui a été réellement trouvé), et `suggestions` (tableau de recommandations d'amélioration). Cette entité encapsule les constats de validation.

### src\domain\entities\ComplianceReport.ts(NOUVEAU)

Références : 

- src\domain\entities\ComplianceIssue.ts(NOUVEAU)

Créer l'entité ComplianceReport pour représenter les résultats globaux de la validation. Inclure les propriétés : `overallStatus` (OK, À_AMÉLIORER, PROBLÈMES_MAJEURS), `totalCriteria` (nombre de critères EARS analysés), `implementedCriteria` (nombre avec preuve), `issues` (tableau de ComplianceIssue), `suggestions` (recommandations générales), `strengths` (points forts bien implémentés), `coveragePercentage` (ratio critères implémentés / total), et `generatedAt` (timestamp). Cette entité agrège tous les constats de validation dans un rapport complet.

### src\domain\entities\index.ts(MODIFIER)

Références : 

- src\domain\entities\EarsCriterion.ts(NOUVEAU)
- src\domain\entities\CodeEvidence.ts(NOUVEAU)
- src\domain\entities\ComplianceIssue.ts(NOUVEAU)
- src\domain\entities\ComplianceReport.ts(NOUVEAU)

Mettre à jour le fichier d'export des entités du domaine pour inclure les nouvelles entités de validation. Ajouter les exports pour EarsCriterion, CodeEvidence, ComplianceIssue et ComplianceReport en plus des exports existants ChatMessage et PromptDefinition. Cela maintient une interface propre de la couche domaine tout en exposant les nouveaux concepts de validation.

### src\application\ports\SpecParserPort.ts(NOUVEAU)

Références : 

- src\domain\entities\EarsCriterion.ts(NOUVEAU)
- .claude\templates\requirements-template.md

Créer l'interface SpecParserPort qui définit le contrat pour le parsing des documents de spécification afin d'extraire les critères EARS. Inclure les méthodes : `parseRequirements(filePath: string): Promise<EarsCriterion[]>` pour extraire les motifs EARS depuis requirements.md, `parseDesign(filePath: string): Promise<DesignComponent[]>` pour extraire les composants de design, et `validateDocumentStructure(filePath: string, templatePath: string): Promise<StructureValidation>` pour vérifier la conformité au template. Ce port sera implémenté par des adaptateurs d'infrastructure qui gèrent la logique réelle de parsing.

### src\application\ports\CodeAnalyzerPort.ts(NOUVEAU)

Références : 

- src\domain\entities\EarsCriterion.ts(NOUVEAU)
- src\domain\entities\CodeEvidence.ts(NOUVEAU)
- src\domain\entities\ComplianceIssue.ts(NOUVEAU)
- src\domain\entities\ComplianceReport.ts(NOUVEAU)

Créer l'interface CodeAnalyzerPort qui définit le contrat pour l'analyse statique du code. Inclure les méthodes : `analyzeCodebase(rootPath: string): Promise<CodeEvidence[]>` pour scanner tous les fichiers de code et extraire les symboles, `findEvidenceForCriteria(criteria: EarsCriterion[], codeEvidence: CodeEvidence[]): Promise<ComplianceIssue[]>` pour associer critères EARS et preuves de code, et `generateComplianceReport(criteria: EarsCriterion[], issues: ComplianceIssue[]): Promise<ComplianceReport>` pour créer le rapport final de validation. Ce port abstrait les détails de l'implémentation de l'analyse statique.

### src\application\ports\FileSystemPort.ts(NOUVEAU)

Créer l'interface FileSystemPort pour les opérations sur les fichiers nécessaires au système de validation. Inclure les méthodes : `readFile(filePath: string): Promise<string>` pour lire le contenu d'un document, `listFiles(directoryPath: string, extensions: string[]): Promise<string[]>` pour découvrir les fichiers de code, `fileExists(filePath: string): Promise<boolean>` pour vérifier l'existence d'un fichier, et `getFileStats(filePath: string): Promise<FileStats>` pour les métadonnées de fichier. Ce port découple la couche applicative des APIs Node.js et facilite les tests.

### src\application\ports\index.ts(MODIFIER)

Références : 

- src\application\ports\SpecParserPort.ts(NOUVEAU)
- src\application\ports\CodeAnalyzerPort.ts(NOUVEAU)
- src\application\ports\FileSystemPort.ts(NOUVEAU)

Mettre à jour le fichier d'export des ports applicatifs pour inclure les nouveaux ports de validation. Ajouter les exports pour SpecParserPort, CodeAnalyzerPort et FileSystemPort en plus de l'export existant PromptBuilderPort. Cela maintient une interface propre de la couche applicative tout en exposant les nouveaux contrats de validation.

### src\application\use-cases\ValidateSpecComplianceUseCase.ts(NOUVEAU)

Références : 

- src\application\ports\SpecParserPort.ts(NOUVEAU)
- src\application\ports\CodeAnalyzerPort.ts(NOUVEAU)
- src\application\ports\FileSystemPort.ts(NOUVEAU)
- src\domain\entities\ComplianceReport.ts(NOUVEAU)

Créer le cas d'utilisation ValidateSpecComplianceUseCase qui orchestre le workflow complet de validation spec-vers-code. Le cas d'utilisation : 1) utilise FileSystemPort pour vérifier l'existence de specs/requirements.md et specs/design.md, 2) utilise SpecParserPort pour extraire les critères EARS depuis requirements, 3) utilise CodeAnalyzerPort pour scanner la base de code et trouver des preuves, 4) associe critères et preuves pour identifier les problèmes de conformité, 5) génère un ComplianceReport complet avec des suggestions actionnables. Inclure la gestion des erreurs pour les fichiers manquants et les échecs de parsing. Ce cas d'utilisation encapsule la logique métier centrale de la validation spec-code.

### src\application\use-cases\index.ts(MODIFIER)

Références : 

- src\application\use-cases\ValidateSpecComplianceUseCase.ts(NOUVEAU)

Mettre à jour le fichier d'export des cas d'utilisation pour inclure le nouveau ValidateSpecComplianceUseCase en plus des exports existants GenerateRequirementsUseCase, GenerateDesignFromRequirementsUseCase et GenerateCodeFromDesignUseCase. Cela maintient une interface propre de la couche applicative tout en exposant le nouveau cas d'utilisation de validation.

### src\infrastructure\adapters\MarkdownEarsParser.ts(NOUVEAU)

Références : 

- src\application\ports\SpecParserPort.ts(NOUVEAU)
- src\domain\entities\EarsCriterion.ts(NOUVEAU)
- .claude\templates\requirements-template.md

Créer MarkdownEarsParser qui implémente l'interface SpecParserPort. Cet adaptateur va parser les documents markdown en français pour extraire les critères EARS à l'aide de regex. Implémenter la logique pour : 1) lire le contenu markdown et identifier les sections, 2) utiliser des regex pour détecter les formats EARS comme `QUAND [événement] ALORS [système] DOIT [réponse]` et `SI [précondition] ALORS [système] DOIT [réponse]`, 3) extraire les événements déclencheurs, réponses système et conditions, 4) générer des IDs uniques pour chaque critère, 5) suivre les sections source et numéros de ligne pour la traçabilité. Inclure la gestion des erreurs pour les énoncés EARS mal formés et fournir un retour détaillé sur le parsing.

### src\infrastructure\adapters\TypeScriptAnalyzer.ts(NOUVEAU)

Références : 

- src\application\ports\CodeAnalyzerPort.ts(NOUVEAU)
- src\domain\entities\CodeEvidence.ts(NOUVEAU)
- src\domain\entities\ComplianceReport.ts(NOUVEAU)

Créer TypeScriptAnalyzer qui implémente l'interface CodeAnalyzerPort en utilisant la bibliothèque ts-morph pour l'analyse statique. Cet adaptateur : 1) parcourt récursivement les fichiers TypeScript du dossier src/, 2) extrait fonctions, classes, interfaces et variables avec leurs signatures, 3) analyse les noms de fonctions, paramètres et types de retour pour la correspondance sémantique avec les critères EARS, 4) recherche des mots-clés et motifs indiquant l'implémentation de requirements spécifiques (ex : fonctions de validation pour les requirements 'DOIT'), 5) génère des objets CodeEvidence avec des scores de confiance basés sur les conventions de nommage et motifs de code, 6) fournit des extraits de code détaillés et des informations de localisation pour la traçabilité. Inclure le support des motifs TypeScript courants et frameworks.

### src\infrastructure\adapters\NodeFileSystemAdapter.ts(NOUVEAU)

Références : 

- src\application\ports\FileSystemPort.ts(NOUVEAU)

Créer NodeFileSystemAdapter qui implémente l'interface FileSystemPort en utilisant les APIs Node.js fs/promises. Cet adaptateur : 1) fournit la lecture de fichiers asynchrone avec gestion des erreurs pour les fichiers manquants, 2) implémente la traversée de répertoires avec filtrage par extension pour la découverte de code, 3) gère la résolution des chemins relatifs à la racine du projet, 4) inclut la vérification d'existence de fichier et la récupération de métadonnées, 5) fournit des messages d'erreur appropriés pour les problèmes de système de fichiers. Suivre les mêmes modèles que les autres adaptateurs d'infrastructure pour la cohérence.

### src\infrastructure\adapters\index.ts(MODIFIER)

Références : 

- src\infrastructure\adapters\MarkdownEarsParser.ts(NOUVEAU)
- src\infrastructure\adapters\TypeScriptAnalyzer.ts(NOUVEAU)
- src\infrastructure\adapters\NodeFileSystemAdapter.ts(NOUVEAU)

Créer le fichier d'export des adaptateurs d'infrastructure pour exporter MarkdownEarsParser, TypeScriptAnalyzer et NodeFileSystemAdapter. Cela fournit une interface propre à la couche présentation pour importer et instancier les adaptateurs de validation.

### src\infrastructure\index.ts(MODIFIER)

Références : 

- src\infrastructure\adapters\index.ts(MODIFIER)

Mettre à jour le fichier d'export de l'infrastructure pour inclure le nouveau dossier adapters en plus du dossier mcp existant. Ajouter l'export du module adapters pour maintenir une interface propre de la couche infrastructure.

### src\presentation\mcp\McpServerFactory.ts(MODIFIER)

Références : 

- src\application\use-cases\ValidateSpecComplianceUseCase.ts(NOUVEAU)
- src\infrastructure\adapters\index.ts(MODIFIER)
- src\domain\entities\ComplianceReport.ts(NOUVEAU)

Étendre McpServerFactory pour enregistrer le nouveau prompt spec-validate qui inclut la validation de conformité du code. Ajouter les imports pour ValidateSpecComplianceUseCase et les nouveaux adaptateurs d'infrastructure (MarkdownEarsParser, TypeScriptAnalyzer, NodeFileSystemAdapter). Instancier ces adaptateurs et les injecter dans ValidateSpecComplianceUseCase. Enregistrer un nouveau prompt MCP `spec-validate` avec un schéma pour un paramètre optionnel de nom de fonctionnalité. Le handler du prompt exécutera le cas d'utilisation et formatra le ComplianceReport en réponses ChatMessage, en suivant le même modèle de réponse que les prompts existants. Suivre le modèle d'injection de dépendances utilisé pour les autres cas d'utilisation.

### package.json(MODIFIER)

Références : 

- src\infrastructure\adapters\TypeScriptAnalyzer.ts(NOUVEAU)

Ajouter ts-morph comme dépendance pour les capacités d'analyse statique TypeScript. Ajouter la dépendance `"ts-morph": "^20.0.0"` dans la section dependencies. Cette bibliothèque fournit un wrapper du compilateur TypeScript qui facilite l'analyse et la manipulation du code TypeScript de manière programmatique, ce qui est essentiel pour la fonctionnalité d'analyse statique du code.

### .claude\commands\spec-validate-compliance.md(NOUVEAU)

Références : 

- .claude\agents\spec-requirements-validator.md
- .claude\templates\requirements-template.md

Créer la spécification de commande pour la commande améliorée `/spec-validate` qui inclut la validation de conformité du code. Documenter l'utilisation de la commande pour valider la cohérence entre les critères EARS des spécifications et l'implémentation réelle du code. Spécifier que cette commande : 1) parse requirements.md pour extraire les critères EARS, 2) analyse la base de code TypeScript pour trouver des preuves d'implémentation, 3) génère des rapports de conformité avec pourcentages de couverture, 4) fournit des suggestions actionnables pour améliorer l'alignement spec-code. Inclure le format de sortie attendu montrant le statut de conformité, les implémentations manquantes et les recommandations d'amélioration. Référencer l'intégration avec les agents de validation existants et les nouvelles capacités d'analyse statique.