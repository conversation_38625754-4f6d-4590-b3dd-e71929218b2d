import { TemplateProviderPort } from '../ports/TemplateProviderPort.js';
import { CodeGeneratorPort } from '../ports/CodeGeneratorPort.js';
import { SpecDocument } from '../../domain/entities/SpecDocument.js';
import { CodeSkeleton } from '../../domain/entities/CodeSkeleton.js';

/**
 * Cas d'utilisation pour créer un package de spécification complet.
 * Orchestre le workflow complet : exigences → conception → squelettes de code.
 */
export class CreateFullSpecUseCase {
  constructor(
    private templateProvider: TemplateProviderPort,
    private codeGenerator: CodeGeneratorPort
  ) {}

  /**
   * Exécute le workflow complet de création de spécifications
   * @param featureName Le nom de la fonctionnalité
   * @param description La description de la fonctionnalité
   * @returns Un objet contenant tous les documents et squelettes générés
   */
  async execute(featureName: string, description: string): Promise<{
    requirements: SpecDocument;
    design: SpecDocument;
    codeSkeletons: CodeSkeleton[];
    projectStructure: string[];
  }> {
    // 1. Générer le document d'exigences à partir du modèle
    const requirementsTemplate = await this.templateProvider.getTemplate('requirements-template');
    const requirementsContent = this.generateRequirementsFromTemplate(
      requirementsTemplate, 
      featureName, 
      description
    );
    
    const requirements: SpecDocument = {
      type: 'requirements',
      content: requirementsContent,
      featureName,
      status: 'draft'
    };

    // 2. Générer le document de conception à partir du modèle
    const designTemplate = await this.templateProvider.getTemplate('design-template');
    const designContent = this.generateDesignFromTemplate(
      designTemplate, 
      featureName, 
      description, 
      requirementsContent
    );
    
    const design: SpecDocument = {
      type: 'design',
      content: designContent,
      featureName,
      status: 'draft'
    };

    // 3. Générer les squelettes de code à partir de la conception
    const codeSkeletons = await this.codeGenerator.generateCodeSkeletons(design);
    
    // 4. Générer la structure du projet
    const projectStructure = await this.codeGenerator.generateProjectStructure(design);

    return {
      requirements,
      design,
      codeSkeletons,
      projectStructure
    };
  }

  /**
   * Génère le contenu des exigences à partir du modèle
   */
  private generateRequirementsFromTemplate(
    template: string, 
    featureName: string, 
    description: string
  ): string {
    return template
      .replace(/\{\{featureName\}\}/g, featureName)
      .replace(/\{\{description\}\}/g, description)
      .replace(/\{\{date\}\}/g, new Date().toISOString().split('T')[0]);
  }

  /**
   * Génère le contenu de conception à partir du modèle
   */
  private generateDesignFromTemplate(
    template: string, 
    featureName: string, 
    description: string,
    requirementsContent: string
  ): string {
    return template
      .replace(/\{\{featureName\}\}/g, featureName)
      .replace(/\{\{description\}\}/g, description)
      .replace(/\{\{requirements\}\}/g, requirementsContent)
      .replace(/\{\{date\}\}/g, new Date().toISOString().split('T')[0]);
  }
}
