# Commande de Configuration du Pilotage des Spécifications

Créez ou mettez à jour des documents de pilotage qui fournissent un contexte projet persistant.

## Utilisation
```
/spec-project-setup
```

## Instructions
Vous aidez à mettre en place des documents de pilotage qui guideront tous les développements de spécifications futurs. Ces documents fournissent un contexte persistant sur la vision produit, la pile technologique et la structure du projet.

## Processus

1. **Vérifier l’Existence de Documents de Pilotage**
   - Cherchez le répertoire `.sdd/project/`
   - Vérifiez l’existence des fichiers product.md, tech.md, structure.md
   - S’ils existent, chargez et affichez le contenu actuel

2. **Analyser le Projet**
   - Passez en revue la base de code pour comprendre :
     - Type et objectif du projet
     - Pile technologique utilisée
     - Structure des dossiers et des modèles
     - Conventions de codage
     - Fonctionnalités et caractéristiques existantes
   - Recherchez :
     - package.json, requirements.txt, go.mod, etc.
     - Fichiers README
     - Fichiers de configuration
     - Structure du code source

3. **Présenter les Détails Déduits**
   - Montrez à l’utilisateur ce que vous avez appris sur :
     - **Produit** : Objectif, fonctionnalités, utilisateurs cibles
     - **Technologie** : Frameworks, bibliothèques, outils
     - **Structure** : Organisation des fichiers, conventions de nommage
   - Format :
     ```
     D’après mon analyse, voici ce que j’ai déduit :
     
     **Détails du Produit :**
     - [Détail déduit 1]
     - [Détail déduit 2]
     
     **Pile Technologique :**
     - [Technologie déduite 1]
     - [Technologie déduite 2]
     
     **Structure du Projet :**
     - [Modèle déduit 1]
     - [Modèle déduit 2]
     ```
   - Demandez : « Ces détails déduits vous semblent-ils corrects ? Merci d’indiquer lesquels garder ou supprimer. »

4. **Recueillir les Informations Manquantes**
   - Selon les retours de l’utilisateur, identifiez les lacunes
   - Posez des questions ciblées pour compléter les informations :
     
     **Questions Produit :**
     - Quel est le principal problème que ce produit résout ?
     - Qui sont les utilisateurs principaux ?
     - Quels sont les objectifs métier clés ?
     - Quels indicateurs définissent le succès ?
     
     **Questions Technologie :**
     - Y a-t-il des contraintes ou exigences techniques ?
     - Quels services tiers sont intégrés ?
     - Quelles sont les exigences de performance ?
     
     **Questions Structure :**
     - Y a-t-il des standards de codage spécifiques à suivre ?
     - Comment organiser les nouvelles fonctionnalités ?
     - Quelles sont les exigences de tests ?

5. **Générer les Documents de Pilotage**
   - Créez le répertoire `.sdd/project/` s’il n’existe pas
   - Générez trois fichiers selon les modèles et les informations recueillies :
     
     **product.md** : Vision produit, utilisateurs, fonctionnalités, objectifs
     **tech.md** : Pile technologique, outils, contraintes, décisions
     **structure.md** : Organisation des fichiers, conventions de nommage, modèles

6. **Revoir et Confirmer**
   - Présentez les documents générés à l’utilisateur
   - Demandez une validation finale avant sauvegarde
   - Effectuez les ajustements demandés

## Notes Importantes

- **Les documents de pilotage sont persistants** – ils seront référencés dans toutes les commandes de spécifications futures
- **Gardez les documents ciblés** – chacun doit couvrir son domaine spécifique
- **Mettez à jour régulièrement** – les documents de pilotage doivent évoluer avec le projet
- **Ne jamais inclure de données sensibles** – pas de mots de passe, clés API ou identifiants

## Exemple de Flux

1. Analyse du projet et identification d’une application React/TypeScript
2. Présentation des détails déduits sur la plateforme e-commerce
3. L’utilisateur confirme la plupart des détails mais précise le marché cible
4. Questions sur les exigences de performance et les services tiers
5. Génération des documents de pilotage avec toutes les informations recueillies
6. L’utilisateur relit et approuve les documents
7. Sauvegarde dans le répertoire `.sdd/project/`

## Prochaines Étapes
Après la création des documents de pilotage, ils seront automatiquement référencés lors de :
- `/spec-create` – Alignement des exigences avec la vision produit
- `/spec-design` – Respect des modèles technologiques établis
- `/spec-tasks` – Utilisation de la bonne organisation des fichiers
- `/spec-execute` – Implémentation selon toutes les conventions
