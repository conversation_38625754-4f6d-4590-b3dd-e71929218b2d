import { ValidationAgentPort } from '../ports/ValidationAgentPort.js';
import { SpecDocument } from '../../domain/entities/SpecDocument.js';
import { ValidationReport } from '../../domain/entities/ValidationReport.js';

/**
 * Cas d'utilisation pour valider la cohérence entre les spécifications et le code.
 * Utilise les agents de validation pour vérifier la qualité et la cohérence.
 */
export class ValidateSpecUseCase {
  constructor(private validationAgent: ValidationAgentPort) {}

  /**
   * Exécute la validation complète des spécifications
   * @param requirementsDoc Le document d'exigences
   * @param designDoc Le document de conception
   * @param codeFiles Les fichiers de code existants (optionnel)
   * @returns Un rapport de validation consolidé
   */
  async execute(
    requirementsDoc: SpecDocument,
    designDoc: SpecDocument,
    codeFiles: string[] = []
  ): Promise<{
    overallStatus: "PASS" | "BESOIN_AMÉLIORATION" | "PROBLÈMES_MAJEURS";
    requirementsValidation: ValidationReport;
    designValidation: ValidationReport;
    crossValidation?: ValidationReport;
    summary: string;
  }> {
    // 1. Valider le document d'exigences
    const requirementsValidation = await this.validationAgent.validateRequirements(requirementsDoc);
    
    // 2. Valider le document de conception
    const designValidation = await this.validationAgent.validateDesign(designDoc);
    
    // 3. Validation croisée si du code existe
    let crossValidation: ValidationReport | undefined;
    if (codeFiles.length > 0) {
      crossValidation = await this.validationAgent.validateCrossConsistency(
        requirementsDoc,
        designDoc,
        codeFiles
      );
    }

    // 4. Déterminer le statut global
    const overallStatus = this.determineOverallStatus(
      requirementsValidation,
      designValidation,
      crossValidation
    );

    // 5. Générer un résumé
    const summary = this.generateSummary(
      requirementsValidation,
      designValidation,
      crossValidation,
      overallStatus
    );

    return {
      overallStatus,
      requirementsValidation,
      designValidation,
      crossValidation,
      summary
    };
  }

  /**
   * Détermine le statut global basé sur les validations individuelles
   */
  private determineOverallStatus(
    requirementsValidation: ValidationReport,
    designValidation: ValidationReport,
    crossValidation?: ValidationReport
  ): "PASS" | "BESOIN_AMÉLIORATION" | "PROBLÈMES_MAJEURS" {
    const validations = [requirementsValidation, designValidation];
    if (crossValidation) {
      validations.push(crossValidation);
    }

    // Si au moins une validation a des problèmes majeurs
    if (validations.some(v => v.status === "PROBLÈMES_MAJEURS")) {
      return "PROBLÈMES_MAJEURS";
    }

    // Si au moins une validation nécessite des améliorations
    if (validations.some(v => v.status === "BESOIN_AMÉLIORATION")) {
      return "BESOIN_AMÉLIORATION";
    }

    // Toutes les validations sont passées
    return "PASS";
  }

  /**
   * Génère un résumé textuel de la validation
   */
  private generateSummary(
    requirementsValidation: ValidationReport,
    designValidation: ValidationReport,
    crossValidation: ValidationReport | undefined,
    overallStatus: string
  ): string {
    const totalIssues = requirementsValidation.issues.length + 
                       designValidation.issues.length + 
                       (crossValidation?.issues.length || 0);
    
    const totalSuggestions = requirementsValidation.suggestions.length + 
                            designValidation.suggestions.length + 
                            (crossValidation?.suggestions.length || 0);

    let summary = `Statut global: ${overallStatus}\n\n`;
    summary += `Problèmes identifiés: ${totalIssues}\n`;
    summary += `Suggestions d'amélioration: ${totalSuggestions}\n\n`;
    
    if (overallStatus === "PASS") {
      summary += "✅ Toutes les validations sont passées avec succès.";
    } else if (overallStatus === "BESOIN_AMÉLIORATION") {
      summary += "⚠️ Des améliorations sont recommandées pour optimiser la qualité.";
    } else {
      summary += "❌ Des problèmes majeurs nécessitent une attention immédiate.";
    }

    return summary;
  }
}
