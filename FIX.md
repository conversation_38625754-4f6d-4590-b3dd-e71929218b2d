Fix the prompt registration in `McpServerFactory.ts` by either: 1) Renaming the prompt to `spec-validate-compliance` to match the handler, or 2) Creating a separate registration for the compliance validation prompt. Ensure the prompt name, handler method, and functionality are properly aligned.

Improve error handling in `TypeScriptAnalyzer.ts` by: 1) Adding validation for tsconfig.json existence before creating the Project, 2) Providing a fallback configuration if tsconfig.json is missing, 3) Adding proper error handling for Project initialization failures, and 4) Logging warnings when configuration issues are detected.

Enhance the EARS parsing regex patterns in `MarkdownEarsParser.ts` to handle more French language variations: 1) Make patterns case-insensitive, 2) Add support for different punctuation, 3) Handle multi-line EARS statements, 4) Include more article variations, and 5) Add comprehensive test cases for edge cases.

Optimize the code analysis performance in `TypeScriptAnalyzer.ts` and `NodeFileSystemAdapter.ts` by: 1) Implementing file processing in batches, 2) Adding memory usage monitoring, 3) Implementing async/await patterns for non-blocking analysis, 4) Adding configurable limits for file count and directory depth, and 5) Providing progress feedback for long-running analyses.
