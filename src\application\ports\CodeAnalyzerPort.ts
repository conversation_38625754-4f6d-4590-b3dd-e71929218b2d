import { EarsCriterion } from '../../domain/entities/EarsCriterion.js';
import { CodeEvidence } from '../../domain/entities/CodeEvidence.js';
import { ComplianceIssue } from '../../domain/entities/ComplianceIssue.js';
import { ComplianceReport } from '../../domain/entities/ComplianceReport.js';

export interface CodeAnalyzerPort {
  analyzeCodebase(rootPath: string): Promise<CodeEvidence[]>;
  findEvidenceForCriteria(criteria: EarsCriterion[], codeEvidence: CodeEvidence[]): Promise<ComplianceIssue[]>;
  generateComplianceReport(criteria: EarsCriterion[], issues: ComplianceIssue[]): Promise<ComplianceReport>;
}
