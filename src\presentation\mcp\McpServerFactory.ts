import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import {
  GenerateRequirementsUseCase,
  GenerateDesignFromRequirementsUseCase,
  GenerateCodeFromDesignUseCase,
  CreateFullSpecUseCase,
  ValidateSpecUseCase
} from '../../application/use-cases/index.js';
import { McpPromptAdapter } from '../../infrastructure/mcp/McpPromptAdapter.js';
import {
  FileSystemTemplateProvider,
  McpValidationAdapter,
  BasicCodeGenerator
} from '../../infrastructure/adapters/index.js';
import { McpPromptHandlers } from './McpPromptHandlers.js';

/**
 * Factory pour créer et configurer le serveur MCP avec l'architecture hexagonale étendue.
 * Supporte maintenant 5 prompts : 3 existants + 2 nouveaux (spec-create-full, spec-validate).
 */
export class McpServerFactory {
  /**
   * Crée et configure un serveur MCP avec tous les cas d'utilisation et adaptateurs
   * @returns Le serveur MCP configuré
   */
  static createServer(): McpServer {
    const server = new McpServer({
      name: "Spec-Driven Development MCP Server",
      version: "0.1.0",
    });

    // Instanciation des adaptateurs d'infrastructure
    const promptAdapter = new McpPromptAdapter();
    const templateProvider = new FileSystemTemplateProvider();
    const validationAdapter = new McpValidationAdapter();
    const codeGenerator = new BasicCodeGenerator();

    // Instanciation des cas d'utilisation avec injection de dépendance
    const generateRequirementsUseCase = new GenerateRequirementsUseCase(promptAdapter);
    const generateDesignUseCase = new GenerateDesignFromRequirementsUseCase(promptAdapter);
    const generateCodeUseCase = new GenerateCodeFromDesignUseCase(promptAdapter);
    const createFullSpecUseCase = new CreateFullSpecUseCase(templateProvider, codeGenerator);
    const validateSpecUseCase = new ValidateSpecUseCase(validationAdapter);

    // Instanciation des gestionnaires de prompts
    const promptHandlers = new McpPromptHandlers(
      generateRequirementsUseCase,
      generateDesignUseCase,
      generateCodeUseCase,
      createFullSpecUseCase,
      validateSpecUseCase
    );

    // Enregistrement des prompts existants
    server.registerPrompt(
      "generate-requirements",
      {
        title: "Generate Requirements Document",
        description: "Generate requirements.md using EARS format",
        argsSchema: {
          requirements: z.string().describe("High-level requirements of the application. Example: 'A Vue.js todo application with task creation, completion tracking, and local storage persistence'")
        }
      },
      promptHandlers.handleGenerateRequirements
    );

    server.registerPrompt(
      "generate-design-from-requirements",
      {
        title: "Generate Design Document from Requirements Document",
        description: "Generate design.md from requirements.md",
      },
      promptHandlers.handleGenerateDesignFromRequirements
    );

    server.registerPrompt(
      "generate-code-from-design",
      {
        title: "Generate Code from Design Document",
        description: "Generate code from design.md"
      },
      promptHandlers.handleGenerateCodeFromDesign
    );

    // Enregistrement des nouveaux prompts
    server.registerPrompt(
      "spec-create-full",
      {
        title: "Create Full Specification Package",
        description: "Generate complete specification package: requirements.md + design.md + code skeletons",
        argsSchema: {
          featureName: z.string().describe("Name of the feature to create specifications for"),
          description: z.string().describe("Detailed description of the feature requirements and functionality")
        }
      },
      promptHandlers.handleSpecCreateFull
    );

    server.registerPrompt(
      "spec-validate",
      {
        title: "Validate Specification Consistency",
        description: "Validate consistency between specifications and code using validation agents",
        argsSchema: {
          requirementsPath: z.string().optional().describe("Path to requirements.md file (optional)"),
          designPath: z.string().optional().describe("Path to design.md file (optional)"),
          codeFilesJson: z.string().optional().describe("JSON string array of code file paths to validate against (optional)")
        }
      },
      promptHandlers.handleSpecValidate
    );

    return server;
  }
}
