/**
 * Entité du domaine représentant la définition d'un prompt.
 * Cette entité encapsule les métadonnées d'un prompt indépendamment de l'implémentation MCP.
 */
export interface PromptDefinition {
  /**
   * Le nom unique du prompt
   */
  name: string;
  
  /**
   * Le titre affiché du prompt
   */
  title: string;
  
  /**
   * La description du prompt
   */
  description: string;
  
  /**
   * Le schéma des arguments (optionnel)
   */
  argsSchema?: Record<string, any>;
}
