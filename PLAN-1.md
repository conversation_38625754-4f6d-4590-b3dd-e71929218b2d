I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current codebase is a simple MCP server with all business logic concentrated in `server.ts`. It exposes 3 prompts for spec-driven development workflow. The code needs to be restructured following hexagonal architecture principles while maintaining full compatibility with the existing MCP API. The main challenge is properly separating concerns without breaking the current JSON-RPC contracts.

### Approach

I'll restructure the codebase using hexagonal architecture by creating four distinct layers: domain (pure business logic), application (use cases), infrastructure (external adapters), and presentation (MCP interface). The strategy is to extract business logic from `server.ts` into appropriate layers while keeping the same public API. Each MCP prompt will become a use case, with proper interfaces (ports) to decouple dependencies. The refactoring will be done incrementally to ensure compatibility is maintained throughout.

### Reasoning

I explored the repository structure and read all the relevant source files to understand the current architecture. The codebase consists of a simple MCP server with three prompts for generating requirements, design documents, and code. I analyzed the dependencies, business logic, and identified the key integration points that need to be preserved during the hexagonal restructuring.

## Mermaid Diagram

sequenceDiagram
    participant Client as MCP Client
    participant Presentation as Presentation Layer<br/>(McpServerFactory)
    participant Application as Application Layer<br/>(Use Cases)
    participant Domain as Domain Layer<br/>(Entities)
    participant Infrastructure as Infrastructure Layer<br/>(MCP Adapter)

    Client->>Presentation: MCP Prompt Request<br/>(generate-requirements)
    Presentation->>Application: Execute Use Case<br/>(GenerateRequirementsUseCase)
    Application->>Domain: Create ChatMessage<br/>entities
    Domain-->>Application: Return ChatMessage<br/>objects
    Application->>Infrastructure: Build MCP Response<br/>(via PromptBuilderPort)
    Infrastructure-->>Application: MCP-formatted response
    Application-->>Presentation: Use case result
    Presentation-->>Client: MCP Prompt Response

    Note over Presentation,Infrastructure: Clean separation of concerns<br/>with dependency inversion
    Note over Domain: Pure business logic<br/>no external dependencies
    Note over Infrastructure: MCP SDK integration<br/>implements ports from application

## Proposed File Changes

### src\domain(NEW)

Create the domain layer directory to contain pure business logic, entities, and value objects without any external dependencies.

### src\domain\index.ts(NEW)

Create barrel export file for the domain layer to expose all domain entities and value objects.

### src\domain\entities(NEW)

Create entities directory to contain domain entities like ChatMessage and PromptDefinition.

### src\domain\entities\ChatMessage.ts(NEW)

Create ChatMessage entity to represent the structure of chat messages used in MCP prompts. This will be a simple value object with properties like `role`, `content` with proper typing but no external dependencies.

### src\domain\entities\PromptDefinition.ts(NEW)

Create PromptDefinition entity to encapsulate prompt metadata like name, title, description, and argument schema. This represents the core business concept of what a prompt is, independent of the MCP SDK implementation.

### src\domain\entities\index.ts(NEW)

References: 

- src\domain\entities\ChatMessage.ts(NEW)
- src\domain\entities\PromptDefinition.ts(NEW)

Create barrel export file for domain entities to export ChatMessage and PromptDefinition.

### src\application(NEW)

Create the application layer directory to contain use cases, ports (interfaces), and application services that orchestrate business logic.

### src\application\index.ts(NEW)

Create barrel export file for the application layer to expose use cases and ports.

### src\application\ports(NEW)

Create ports directory to contain interface definitions that decouple the application layer from infrastructure concerns.

### src\application\ports\PromptBuilderPort.ts(NEW)

Create PromptBuilderPort interface that defines the contract for building MCP prompt responses. This interface will be implemented by infrastructure adapters and used by application use cases to remain decoupled from MCP SDK specifics.

### src\application\ports\index.ts(NEW)

References: 

- src\application\ports\PromptBuilderPort.ts(NEW)

Create barrel export file for application ports to export PromptBuilderPort interface.

### src\application\use-cases(NEW)

Create use-cases directory to contain application use cases that implement the business workflows.

### src\application\use-cases\GenerateRequirementsUseCase.ts(NEW)

References: 

- src\server.ts(MODIFY)
- src\application\ports\PromptBuilderPort.ts(NEW)
- src\domain\entities\ChatMessage.ts(NEW)

Create GenerateRequirementsUseCase that encapsulates the business logic for generating requirements documents. This use case will take requirements input and return a ChatMessage array, using the PromptBuilderPort for any external interactions. The logic will be extracted from the current `generate-requirements` prompt in `src/server.ts`.

### src\application\use-cases\GenerateDesignFromRequirementsUseCase.ts(NEW)

References: 

- src\server.ts(MODIFY)
- src\application\ports\PromptBuilderPort.ts(NEW)
- src\domain\entities\ChatMessage.ts(NEW)

Create GenerateDesignFromRequirementsUseCase that handles the business logic for generating design documents from requirements. This use case will return appropriate ChatMessage array for the design generation workflow. The logic will be extracted from the current `generate-design-from-requirements` prompt in `src/server.ts`.

### src\application\use-cases\GenerateCodeFromDesignUseCase.ts(NEW)

References: 

- src\server.ts(MODIFY)
- src\application\ports\PromptBuilderPort.ts(NEW)
- src\domain\entities\ChatMessage.ts(NEW)

Create GenerateCodeFromDesignUseCase that implements the business logic for generating code from design documents. This use case will return the appropriate ChatMessage array for code generation. The logic will be extracted from the current `generate-code-from-design` prompt in `src/server.ts`.

### src\application\use-cases\index.ts(NEW)

References: 

- src\application\use-cases\GenerateRequirementsUseCase.ts(NEW)
- src\application\use-cases\GenerateDesignFromRequirementsUseCase.ts(NEW)
- src\application\use-cases\GenerateCodeFromDesignUseCase.ts(NEW)

Create barrel export file for application use cases to export all three use case classes.

### src\infrastructure(NEW)

Create the infrastructure layer directory to contain adapters that implement ports and handle external dependencies like MCP SDK, file system, etc.

### src\infrastructure\index.ts(NEW)

Create barrel export file for the infrastructure layer to expose adapters and implementations.

### src\infrastructure\mcp(NEW)

Create MCP-specific infrastructure directory to contain adapters that implement MCP SDK integration.

### src\infrastructure\mcp\McpPromptAdapter.ts(NEW)

References: 

- src\application\ports\PromptBuilderPort.ts(NEW)
- src\domain\entities\ChatMessage.ts(NEW)

Create McpPromptAdapter that implements the PromptBuilderPort interface using the MCP SDK. This adapter will handle the translation between domain ChatMessage objects and MCP SDK prompt response format, maintaining compatibility with the existing MCP API contracts.

### src\infrastructure\mcp\index.ts(NEW)

References: 

- src\infrastructure\mcp\McpPromptAdapter.ts(NEW)

Create barrel export file for MCP infrastructure to export McpPromptAdapter.

### src\presentation(NEW)

Create the presentation layer directory to contain entry points and user interface adapters like MCP server factory.

### src\presentation\index.ts(NEW)

Create barrel export file for the presentation layer to expose the MCP server factory and other presentation components.

### src\presentation\mcp(NEW)

Create MCP presentation directory to contain MCP-specific presentation logic like server factory.

### src\presentation\mcp\McpServerFactory.ts(NEW)

References: 

- src\server.ts(MODIFY)
- src\application\use-cases\index.ts(NEW)
- src\infrastructure\mcp\McpPromptAdapter.ts(NEW)

Create McpServerFactory that replaces the `createServer` function from `src/server.ts`. This factory will instantiate all use cases, wire them with infrastructure adapters, and register them with the MCP server. It must maintain the exact same public API signature `(): McpServer` to ensure compatibility with existing entry points in `src/index.ts` and `src/streamableHttp.ts`.

### src\presentation\mcp\index.ts(NEW)

References: 

- src\presentation\mcp\McpServerFactory.ts(NEW)

Create barrel export file for MCP presentation to export McpServerFactory.

### src\server.ts(MODIFY)

References: 

- src\presentation\mcp\McpServerFactory.ts(NEW)
- src\index.ts(MODIFY)
- src\streamableHttp.ts(MODIFY)

Refactor the existing server.ts to use the new hexagonal architecture. Replace the current `createServer` function implementation to delegate to the new `McpServerFactory` from the presentation layer. Remove all business logic that has been moved to use cases, keeping only the factory delegation. Maintain the same export signature to ensure compatibility with `src/index.ts` and `src/streamableHttp.ts`.

### src\index.ts(MODIFY)

References: 

- src\server.ts(MODIFY)

Update the import path to use the refactored `createServer` function from `src/server.ts`. No other changes needed since the function signature remains the same. Verify that the STDIO transport integration continues to work as expected.

### src\streamableHttp.ts(MODIFY)

References: 

- src\server.ts(MODIFY)

Update the import path to use the refactored `createServer` function from `src/server.ts`. No other changes needed since the function signature remains the same. Verify that the HTTP transport integration continues to work as expected.