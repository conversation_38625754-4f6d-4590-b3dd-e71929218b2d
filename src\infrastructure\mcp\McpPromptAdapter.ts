import { PromptBuilderPort } from '../../application/ports/PromptBuilderPort.js';
import { ChatMessage } from '../../domain/entities/ChatMessage.js';

/**
 * Adaptateur qui implémente PromptBuilderPort en utilisant le SDK MCP.
 * Gère la traduction entre les objets ChatMessage du domaine et le format de réponse MCP.
 */
export class McpPromptAdapter implements PromptBuilderPort {
  /**
   * Construit une réponse de prompt MCP à partir d'un tableau de messages de chat
   * @param messages Les messages de chat du domaine
   * @returns La réponse formatée pour MCP
   */
  buildPromptResponse(messages: ChatMessage[]): { messages: any[] } {
    // Conversion directe des messages du domaine vers le format MCP
    // Le format est déjà compatible, donc pas de transformation nécessaire
    return {
      messages: messages.map(message => ({
        role: message.role,
        content: message.content
      }))
    };
  }
}
