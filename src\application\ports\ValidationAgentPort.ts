import { SpecDocument } from '../../domain/entities/SpecDocument.js';
import { ValidationReport } from '../../domain/entities/ValidationReport.js';

/**
 * Port (interface) pour les services de validation.
 * Cette interface définit le contrat pour interagir avec les agents de validation
 * du dossier .claude/agents/
 */
export interface ValidationAgentPort {
  /**
   * Valide un document d'exigences
   * @param document Le document d'exigences à valider
   * @returns Le rapport de validation
   */
  validateRequirements(document: SpecDocument): Promise<ValidationReport>;
  
  /**
   * Valide un document de conception
   * @param document Le document de conception à valider
   * @returns Le rapport de validation
   */
  validateDesign(document: SpecDocument): Promise<ValidationReport>;
  
  /**
   * Effectue une validation croisée entre les spécifications et le code
   * @param requirementsDoc Le document d'exigences
   * @param designDoc Le document de conception
   * @param codeFiles Les fichiers de code existants
   * @returns Le rapport de validation croisée
   */
  validateCrossConsistency(
    requirementsDoc: SpecDocument, 
    designDoc: SpecDocument, 
    codeFiles: string[]
  ): Promise<ValidationReport>;
}
