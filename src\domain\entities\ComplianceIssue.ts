import { EarsCriterion } from './EarsCriterion.js';
import { CodeEvidence } from './CodeEvidence.js';

export interface ComplianceIssue {
  criterionId: string;
  severity: 'CRITICAL' | 'MAJOR' | 'MINOR';
  issueType: 'MISSING_IMPLEMENTATION' | 'PARTIAL_IMPLEMENTATION' | 'INCORRECT_IMPLEMENTATION';
  message: string;
  expectedEvidence: string;
  actualEvidence: CodeEvidence[];
  suggestions: string[];
}
