import {
  GenerateRequirementsUseCase,
  GenerateDesignFromRequirementsUseCase,
  GenerateCodeFromDesignUseCase,
  CreateFullSpecUseCase,
  ValidateSpecUseCase,
  ValidateSpecComplianceUseCase
} from '../../application/use-cases/index.js';
import { SpecDocument } from '../../domain/entities/SpecDocument.js';
import { ComplianceReport } from '../../domain/entities/ComplianceReport.js';

/**
 * Gestionnaires de prompts MCP pour tous les cas d'utilisation.
 * Contient les fonctions de gestion pour les 6 prompts (3 existants + 3 nouveaux).
 */
export class McpPromptHandlers {
  constructor(
    private generateRequirementsUseCase: GenerateRequirementsUseCase,
    private generateDesignUseCase: GenerateDesignFromRequirementsUseCase,
    private generateCodeUseCase: GenerateCodeFromDesignUseCase,
    private createFullSpecUseCase: CreateFullSpecUseCase,
    private validateSpecUseCase: ValidateSpecUseCase,
    private validateSpecComplianceUseCase: ValidateSpecComplianceUseCase
  ) {}

  /**
   * Gestionnaire pour le prompt generate-requirements
   */
  handleGenerateRequirements = ({ requirements }: { requirements: string }) => {
    return this.generateRequirementsUseCase.execute(requirements);
  };

  /**
   * Gestionnaire pour le prompt generate-design-from-requirements
   */
  handleGenerateDesignFromRequirements = () => {
    return this.generateDesignUseCase.execute();
  };

  /**
   * Gestionnaire pour le prompt generate-code-from-design
   */
  handleGenerateCodeFromDesign = () => {
    return this.generateCodeUseCase.execute();
  };

  /**
   * Gestionnaire pour le nouveau prompt spec-create-full
   */
  handleSpecCreateFull = async ({ featureName, description }: { featureName: string; description: string }) => {
    try {
      const result = await this.createFullSpecUseCase.execute(featureName, description);
      
      // Formater la réponse pour MCP
      const responseText = this.formatFullSpecResponse(result);
      
      return {
        messages: [{
          role: "user" as const,
          content: {
            type: "text" as const,
            text: responseText
          }
        }]
      };
    } catch (error) {
      return {
        messages: [{
          role: "user" as const,
          content: {
            type: "text" as const,
            text: `Erreur lors de la création du package de spécification complet: ${error}`
          }
        }]
      };
    }
  };

  /**
   * Gestionnaire pour le nouveau prompt spec-validate
   */
  handleSpecValidate = async ({ requirementsPath, designPath, codeFilesJson }: {
    requirementsPath?: string;
    designPath?: string;
    codeFilesJson?: string
  }) => {
    try {
      // Parser les fichiers de code depuis JSON si fourni
      let codeFiles: string[] = [];
      if (codeFilesJson) {
        try {
          codeFiles = JSON.parse(codeFilesJson);
        } catch {
          codeFiles = [];
        }
      }

      // Créer des documents de spécification factices pour la démonstration
      // Dans une vraie implémentation, ces documents seraient chargés depuis le système de fichiers
      const requirementsDoc: SpecDocument = {
        type: 'requirements',
        content: 'Contenu des exigences à valider...',
        featureName: 'Fonctionnalité à valider',
        status: 'draft'
      };

      const designDoc: SpecDocument = {
        type: 'design',
        content: 'Contenu de la conception à valider...',
        featureName: 'Fonctionnalité à valider',
        status: 'draft'
      };

      const result = await this.validateSpecUseCase.execute(
        requirementsDoc,
        designDoc,
        codeFiles
      );
      
      // Formater la réponse pour MCP
      const responseText = this.formatValidationResponse(result);
      
      return {
        messages: [{
          role: "user" as const,
          content: {
            type: "text" as const,
            text: responseText
          }
        }]
      };
    } catch (error) {
      return {
        messages: [{
          role: "user" as const,
          content: {
            type: "text" as const,
            text: `Erreur lors de la validation des spécifications: ${error}`
          }
        }]
      };
    }
  };

  /**
   * Gestionnaire pour le nouveau prompt spec-validate-compliance
   */
  handleSpecValidateCompliance = async () => {
    try {
      const result = await this.validateSpecComplianceUseCase.execute();

      // Formater la réponse pour MCP
      const responseText = this.formatComplianceResponse(result);

      return {
        messages: [{
          role: "user" as const,
          content: {
            type: "text" as const,
            text: responseText
          }
        }]
      };
    } catch (error) {
      return {
        messages: [{
          role: "user" as const,
          content: {
            type: "text" as const,
            text: `Erreur lors de la validation de conformité: ${error instanceof Error ? error.message : 'Erreur inconnue'}`
          }
        }]
      };
    }
  };

  /**
   * Formate la réponse du cas d'utilisation CreateFullSpec pour MCP
   */
  private formatFullSpecResponse(result: {
    requirements: SpecDocument;
    design: SpecDocument;
    codeSkeletons: any[];
    projectStructure: string[];
  }): string {
    let response = `# Package de Spécification Complet - ${result.requirements.featureName}\n\n`;
    
    response += `## 📋 Document d'Exigences\n\n`;
    response += `${result.requirements.content}\n\n`;
    
    response += `## 🏗️ Document de Conception\n\n`;
    response += `${result.design.content}\n\n`;
    
    response += `## 📁 Structure du Projet\n\n`;
    response += `Dossiers à créer:\n`;
    result.projectStructure.forEach(folder => {
      response += `- ${folder}\n`;
    });
    
    response += `\n## 💻 Squelettes de Code\n\n`;
    result.codeSkeletons.forEach(skeleton => {
      response += `### ${skeleton.filePath}\n`;
      response += `**Description:** ${skeleton.description}\n`;
      response += `**Langage:** ${skeleton.language}\n\n`;
      response += `\`\`\`${skeleton.language}\n${skeleton.content}\n\`\`\`\n\n`;
    });
    
    return response;
  }

  /**
   * Formate la réponse du cas d'utilisation ValidateSpec pour MCP
   */
  private formatValidationResponse(result: {
    overallStatus: string;
    requirementsValidation: any;
    designValidation: any;
    crossValidation?: any;
    summary: string;
  }): string {
    let response = `# Rapport de Validation des Spécifications\n\n`;
    
    response += `## 📊 Résumé\n\n`;
    response += `${result.summary}\n\n`;
    
    response += `## 📋 Validation des Exigences\n\n`;
    response += `**Statut:** ${result.requirementsValidation.status}\n\n`;
    
    if (result.requirementsValidation.issues.length > 0) {
      response += `**Problèmes identifiés:**\n`;
      result.requirementsValidation.issues.forEach((issue: string) => {
        response += `- ❌ ${issue}\n`;
      });
      response += `\n`;
    }
    
    if (result.requirementsValidation.suggestions.length > 0) {
      response += `**Suggestions d'amélioration:**\n`;
      result.requirementsValidation.suggestions.forEach((suggestion: string) => {
        response += `- 💡 ${suggestion}\n`;
      });
      response += `\n`;
    }
    
    if (result.requirementsValidation.strengths.length > 0) {
      response += `**Points forts:**\n`;
      result.requirementsValidation.strengths.forEach((strength: string) => {
        response += `- ✅ ${strength}\n`;
      });
      response += `\n`;
    }
    
    response += `## 🏗️ Validation de la Conception\n\n`;
    response += `**Statut:** ${result.designValidation.status}\n\n`;
    
    if (result.designValidation.issues.length > 0) {
      response += `**Problèmes identifiés:**\n`;
      result.designValidation.issues.forEach((issue: string) => {
        response += `- ❌ ${issue}\n`;
      });
      response += `\n`;
    }
    
    if (result.designValidation.suggestions.length > 0) {
      response += `**Suggestions d'amélioration:**\n`;
      result.designValidation.suggestions.forEach((suggestion: string) => {
        response += `- 💡 ${suggestion}\n`;
      });
      response += `\n`;
    }
    
    if (result.designValidation.strengths.length > 0) {
      response += `**Points forts:**\n`;
      result.designValidation.strengths.forEach((strength: string) => {
        response += `- ✅ ${strength}\n`;
      });
      response += `\n`;
    }
    
    if (result.crossValidation) {
      response += `## 🔄 Validation Croisée\n\n`;
      response += `**Statut:** ${result.crossValidation.status}\n\n`;
      
      if (result.crossValidation.issues.length > 0) {
        response += `**Problèmes de cohérence:**\n`;
        result.crossValidation.issues.forEach((issue: string) => {
          response += `- ❌ ${issue}\n`;
        });
        response += `\n`;
      }
      
      if (result.crossValidation.suggestions.length > 0) {
        response += `**Suggestions:**\n`;
        result.crossValidation.suggestions.forEach((suggestion: string) => {
          response += `- 💡 ${suggestion}\n`;
        });
        response += `\n`;
      }
      
      if (result.crossValidation.strengths.length > 0) {
        response += `**Points forts:**\n`;
        result.crossValidation.strengths.forEach((strength: string) => {
          response += `- ✅ ${strength}\n`;
        });
      }
    }

    return response;
  }

  /**
   * Formate la réponse du cas d'utilisation ValidateSpecCompliance pour MCP
   */
  private formatComplianceResponse(result: ComplianceReport): string {
    let response = `# Rapport de Conformité Spécifications-Code\n\n`;

    // Statut global avec emoji
    const statusEmoji = result.overallStatus === 'PASS' ? '✅' :
                       result.overallStatus === 'NEEDS_IMPROVEMENT' ? '⚠️' : '❌';
    response += `## ${statusEmoji} Statut Global: ${result.overallStatus}\n\n`;

    // Métriques de couverture
    response += `## 📊 Métriques de Couverture\n\n`;
    response += `- **Critères EARS analysés:** ${result.totalCriteria}\n`;
    response += `- **Critères implémentés:** ${result.implementedCriteria}\n`;
    response += `- **Taux de couverture:** ${result.coveragePercentage.toFixed(1)}%\n\n`;

    // Points forts
    if (result.strengths.length > 0) {
      response += `## ✅ Points Forts\n\n`;
      result.strengths.forEach(strength => {
        response += `- ${strength}\n`;
      });
      response += `\n`;
    }

    // Problèmes identifiés
    if (result.issues.length > 0) {
      response += `## ⚠️ Problèmes Identifiés\n\n`;

      const criticalIssues = result.issues.filter(i => i.severity === 'CRITICAL');
      const majorIssues = result.issues.filter(i => i.severity === 'MAJOR');
      const minorIssues = result.issues.filter(i => i.severity === 'MINOR');

      if (criticalIssues.length > 0) {
        response += `### 🚨 Critiques (${criticalIssues.length})\n\n`;
        criticalIssues.forEach(issue => {
          response += `**${issue.criterionId}** - ${issue.message}\n`;
          response += `- **Type:** ${issue.issueType}\n`;
          response += `- **Attendu:** ${issue.expectedEvidence}\n`;
          if (issue.suggestions.length > 0) {
            response += `- **Suggestions:** ${issue.suggestions.join(', ')}\n`;
          }
          response += `\n`;
        });
      }

      if (majorIssues.length > 0) {
        response += `### ⚠️ Majeurs (${majorIssues.length})\n\n`;
        majorIssues.forEach(issue => {
          response += `**${issue.criterionId}** - ${issue.message}\n`;
          response += `- **Type:** ${issue.issueType}\n`;
          if (issue.suggestions.length > 0) {
            response += `- **Suggestions:** ${issue.suggestions.join(', ')}\n`;
          }
          response += `\n`;
        });
      }

      if (minorIssues.length > 0) {
        response += `### 💡 Mineurs (${minorIssues.length})\n\n`;
        minorIssues.forEach(issue => {
          response += `**${issue.criterionId}** - ${issue.message}\n`;
          if (issue.suggestions.length > 0) {
            response += `- **Suggestions:** ${issue.suggestions.join(', ')}\n`;
          }
          response += `\n`;
        });
      }
    }

    // Recommandations générales
    if (result.suggestions.length > 0) {
      response += `## 💡 Recommandations Générales\n\n`;
      result.suggestions.forEach(suggestion => {
        response += `- ${suggestion}\n`;
      });
      response += `\n`;
    }

    // Informations de génération
    response += `---\n`;
    response += `*Rapport généré le ${result.generatedAt.toLocaleString('fr-FR')}*\n`;

    return response;
  }
}
