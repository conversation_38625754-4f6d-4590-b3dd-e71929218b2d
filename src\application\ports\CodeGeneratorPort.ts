import { SpecDocument } from '../../domain/entities/SpecDocument.js';
import { CodeSkeleton } from '../../domain/entities/CodeSkeleton.js';

/**
 * Port (interface) pour la génération de squelettes de code.
 * Cette interface définit le contrat pour créer la structure du code
 * à partir des spécifications de conception.
 */
export interface CodeGeneratorPort {
  /**
   * Génère des squelettes de code à partir d'un document de conception
   * @param designDocument Le document de conception
   * @returns Un tableau de squelettes de code
   */
  generateCodeSkeletons(designDocument: SpecDocument): Promise<CodeSkeleton[]>;
  
  /**
   * Génère la structure de dossiers pour le projet
   * @param designDocument Le document de conception
   * @returns Un tableau de chemins de dossiers à créer
   */
  generateProjectStructure(designDocument: SpecDocument): Promise<string[]>;
}
