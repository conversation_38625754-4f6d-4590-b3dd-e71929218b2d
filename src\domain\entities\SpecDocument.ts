/**
 * Entité du domaine représentant un document de spécification.
 * Cette entité encapsule le concept métier d'un document de spécification,
 * indépendant des préoccupations du système de fichiers.
 */
export interface SpecDocument {
  /**
   * Le type de document de spécification
   */
  type: "requirements" | "design";
  
  /**
   * Le contenu du document
   */
  content: string;
  
  /**
   * Le nom de la fonctionnalité associée
   */
  featureName: string;
  
  /**
   * Le statut du document
   */
  status: "draft" | "validated" | "approved";
}
