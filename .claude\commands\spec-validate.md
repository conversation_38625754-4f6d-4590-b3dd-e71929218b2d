# Commande Spec Validate

Validez la cohérence entre les spécifications et le code en utilisant les agents de validation automatisés.

## Utilisation
```
/spec-validate [chemin-requirements] [chemin-design] [fichiers-code...]
```

## Philosophie de la validation

Cette commande implémente une approche systématique de validation de la qualité et de la cohérence des spécifications, utilisant les agents de validation existants pour garantir l'alignement entre les documents et le code.

### Principes clés
- **Validation automatisée** : Utilise les agents de validation du dossier `.claude/agents/`
- **Cohérence multi-niveaux** : Vérifie l'alignement requirements ↔ design ↔ code
- **Retours constructifs** : Fournit des suggestions d'amélioration spécifiques
- **Standards de qualité** : Applique les critères de qualité établis

## Workflow de validation

**SÉQUENCE DE VALIDATION** : Requirements → Design → Cohérence croisée → Rapport consolidé

### Phase 1 : Validation des exigences
- Utilise l'agent `spec-requirements-validator.md`
- Vérifie la structure et le format EARS
- Contrôle la complétude des user stories
- Valide les critères d'acceptation

### Phase 2 : Validation de la conception
- Utilise l'agent `spec-design-validator.md`
- Vérifie la solidité architecturale
- Contrôle l'alignement avec les exigences
- Valide la faisabilité technique

### Phase 3 : Validation croisée
- Compare requirements ↔ design
- Vérifie la cohérence avec le code existant
- Identifie les écarts et incohérences
- Contrôle l'implémentation des exigences

### Phase 4 : Rapport consolidé
- Synthèse des résultats de validation
- Statut global et recommandations
- Plan d'action pour les améliorations

## Arguments

### chemin-requirements (optionnel)
- **Type** : String
- **Défaut** : `specs/requirements.md` ou détection automatique
- **Description** : Chemin vers le document d'exigences
- **Exemple** : `.sdd/specs/user-auth/requirements.md`

### chemin-design (optionnel)
- **Type** : String
- **Défaut** : `specs/design.md` ou détection automatique
- **Description** : Chemin vers le document de conception
- **Exemple** : `.sdd/specs/user-auth/design.md`

### fichiers-code (optionnel)
- **Type** : Array[String]
- **Défaut** : Détection automatique dans le projet
- **Description** : Liste des fichiers de code à valider
- **Exemple** : `src/auth/*.ts src/components/Auth*.tsx`

## Agents de validation utilisés

### spec-requirements-validator
- **Localisation** : `.claude/agents/spec-requirements-validator.md`
- **Fonction** : Validation de la qualité des exigences
- **Critères** :
  - Structure du document conforme
  - User stories bien formées
  - Critères d'acceptation EARS
  - Complétude et clarté

### spec-design-validator
- **Localisation** : `.claude/agents/spec-design-validator.md`
- **Fonction** : Validation de la conception technique
- **Critères** :
  - Architecture cohérente
  - Composants bien définis
  - Interfaces claires
  - Faisabilité technique

## Critères de validation

### Statuts de validation
- **PASS** : Validation réussie, aucun problème majeur
- **BESOIN_AMÉLIORATION** : Améliorations recommandées
- **PROBLÈMES_MAJEURS** : Problèmes critiques à résoudre

### Catégories d'évaluation

#### Structure et format
- Respect des modèles de spécification
- Organisation logique du contenu
- Formatage et présentation

#### Qualité du contenu
- Clarté et précision des descriptions
- Complétude des informations
- Cohérence interne

#### Alignement technique
- Faisabilité de l'implémentation
- Respect des standards du projet
- Intégration avec l'existant

#### Cohérence croisée
- Alignement requirements ↔ design
- Correspondance design ↔ code
- Traçabilité des exigences

## Format de sortie

### Rapport de validation structuré

```markdown
# Rapport de Validation des Spécifications

## 📊 Résumé
**Statut global:** PASS/BESOIN_AMÉLIORATION/PROBLÈMES_MAJEURS
**Problèmes identifiés:** X
**Suggestions d'amélioration:** Y

## 📋 Validation des Exigences
**Statut:** [STATUT]
**Problèmes identifiés:**
- ❌ [Problème 1]
- ❌ [Problème 2]

**Suggestions d'amélioration:**
- 💡 [Suggestion 1]
- 💡 [Suggestion 2]

**Points forts:**
- ✅ [Point fort 1]
- ✅ [Point fort 2]

## 🏗️ Validation de la Conception
[Structure similaire]

## 🔄 Validation Croisée
[Analyse de cohérence]
```

## Intégration avec les agents

### Chargement des agents
Les agents de validation sont automatiquement chargés depuis :
- `.claude/agents/spec-requirements-validator.md`
- `.claude/agents/spec-design-validator.md`

### Logique de validation
Chaque agent applique sa logique spécifique :
- Analyse du contenu selon ses critères
- Génération de retours structurés
- Attribution d'un statut de validation

## Exemples d'utilisation

### Validation basique
```
/spec-validate
```
Valide les spécifications dans les emplacements par défaut.

### Validation spécifique
```
/spec-validate .sdd/specs/auth/requirements.md .sdd/specs/auth/design.md
```
Valide des documents spécifiques.

### Validation avec code
```
/spec-validate requirements.md design.md src/auth/*.ts
```
Inclut la validation croisée avec le code.

## Cas d'usage

### Avant implémentation
- Vérifier la qualité des spécifications
- S'assurer de la faisabilité technique
- Identifier les lacunes ou ambiguïtés

### Pendant le développement
- Contrôler l'alignement avec les spécifications
- Détecter les dérives d'implémentation
- Maintenir la cohérence

### Après implémentation
- Valider la conformité finale
- Documenter les écarts éventuels
- Préparer la livraison

## Retours et améliorations

### Types de retours
- **Problèmes critiques** : Bloquants pour l'implémentation
- **Suggestions** : Améliorations recommandées
- **Points forts** : Éléments bien réalisés

### Actions recommandées
Selon le statut de validation :
- **PASS** : Procéder à l'implémentation
- **BESOIN_AMÉLIORATION** : Appliquer les suggestions
- **PROBLÈMES_MAJEURS** : Corriger avant de continuer

## Complémentarité

### Avec /spec-create
- Validation après création manuelle
- Contrôle qualité du processus

### Avec /spec-create-full
- Validation après génération automatique
- Vérification de la cohérence générée

### Avec /spec-status
- Suivi de la qualité pendant l'implémentation
- Validation continue du projet

## Automatisation

### Intégration CI/CD
La commande peut être intégrée dans les pipelines :
```bash
# Validation automatique des spécifications
claude-code-spec-workflow validate-specs
```

### Hooks de développement
- Pre-commit : Validation avant commit
- Pre-push : Contrôle avant publication
- PR review : Validation dans les pull requests
