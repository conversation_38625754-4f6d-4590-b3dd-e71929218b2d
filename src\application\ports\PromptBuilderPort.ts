import { ChatMessage } from '../../domain/entities/ChatMessage.js';

/**
 * Port (interface) pour construire les réponses de prompt MCP.
 * Cette interface découple la couche application des spécificités du SDK MCP.
 */
export interface PromptBuilderPort {
  /**
   * Construit une réponse de prompt MCP à partir d'un tableau de messages de chat
   * @param messages Les messages de chat du domaine
   * @returns La réponse formatée pour MCP
   */
  buildPromptResponse(messages: ChatMessage[]): { messages: any[] };
}
