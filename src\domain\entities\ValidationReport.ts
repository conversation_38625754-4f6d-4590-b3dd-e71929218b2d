/**
 * Entité du domaine représentant les résultats de validation.
 * Cette entité encapsule les résultats des agents de validation.
 */
export interface ValidationReport {
  /**
   * Le statut de la validation
   */
  status: "PASS" | "BESOIN_AMÉLIORATION" | "PROBLÈMES_MAJEURS";
  
  /**
   * Les problèmes identifiés lors de la validation
   */
  issues: string[];
  
  /**
   * Les suggestions d'amélioration
   */
  suggestions: string[];
  
  /**
   * Les points forts identifiés
   */
  strengths: string[];
}
