import { SpecParserPort, DesignComponent, StructureValidation } from '../../application/ports/SpecParserPort.js';
import { EarsCriterion } from '../../domain/entities/EarsCriterion.js';
import { FileSystemPort } from '../../application/ports/FileSystemPort.js';

export class MarkdownEarsParser implements SpecParserPort {
  constructor(private fileSystem: FileSystemPort) {}

  async parseRequirements(filePath: string): Promise<EarsCriterion[]> {
    const content = await this.fileSystem.readFile(filePath);
    const lines = content.split('\n');
    const criteria: EarsCriterion[] = [];
    
    let currentSection = '';
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Détecter les sections
      if (line.startsWith('#')) {
        currentSection = line.replace(/^#+\s*/, '');
        continue;
      }
      
      // Patterns EARS en français
      const quandPattern = /QUAND\s+(.+?)\s+ALORS\s+(?:le\s+système|l'application|le\s+serveur)?\s*DOIT\s+(.+)/i;
      const siPattern = /SI\s+(.+?)\s+ALORS\s+(?:le\s+système|l'application|le\s+serveur)?\s*DOIT\s+(.+)/i;
      
      let match = line.match(quandPattern);
      if (match) {
        criteria.push({
          id: `QUAND_${criteria.length + 1}`,
          type: 'QUAND',
          trigger: match[1].trim(),
          response: match[2].trim(),
          sourceSection: currentSection,
          lineNumber: i + 1
        });
        continue;
      }
      
      match = line.match(siPattern);
      if (match) {
        criteria.push({
          id: `SI_${criteria.length + 1}`,
          type: 'SI',
          trigger: match[1].trim(),
          response: match[2].trim(),
          sourceSection: currentSection,
          lineNumber: i + 1
        });
      }
    }
    
    return criteria;
  }

  async parseDesign(filePath: string): Promise<DesignComponent[]> {
    const content = await this.fileSystem.readFile(filePath);
    const components: DesignComponent[] = [];
    
    // Parsing basique des composants de design
    // Cette implémentation peut être étendue selon les besoins
    const componentPattern = /##\s+(.+)/g;
    let match;
    
    while ((match = componentPattern.exec(content)) !== null) {
      components.push({
        name: match[1].trim(),
        type: 'component',
        description: '',
        dependencies: []
      });
    }
    
    return components;
  }

  async validateDocumentStructure(filePath: string, templatePath: string): Promise<StructureValidation> {
    try {
      const content = await this.fileSystem.readFile(filePath);
      const template = await this.fileSystem.readFile(templatePath);
      
      const contentSections = this.extractSections(content);
      const templateSections = this.extractSections(template);
      
      const missingElements: string[] = [];
      
      for (const templateSection of templateSections) {
        if (!contentSections.includes(templateSection)) {
          missingElements.push(templateSection);
        }
      }
      
      return {
        isValid: missingElements.length === 0,
        missingElements,
        suggestions: missingElements.map(section => `Ajouter la section: ${section}`)
      };
    } catch (error) {
      return {
        isValid: false,
        missingElements: [],
        suggestions: [`Erreur lors de la validation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`]
      };
    }
  }

  private extractSections(content: string): string[] {
    const sections: string[] = [];
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('#')) {
        sections.push(trimmed.replace(/^#+\s*/, ''));
      }
    }
    
    return sections;
  }
}
