import { CodeGeneratorPort } from '../../application/ports/CodeGeneratorPort.js';
import { SpecDocument } from '../../domain/entities/SpecDocument.js';
import { CodeSkeleton } from '../../domain/entities/CodeSkeleton.js';

/**
 * Adaptateur qui implémente CodeGeneratorPort pour générer des squelettes de code basiques
 * à partir des documents de conception.
 */
export class BasicCodeGenerator implements CodeGeneratorPort {

  /**
   * Génère des squelettes de code à partir d'un document de conception
   * @param designDocument Le document de conception
   * @returns Un tableau de squelettes de code
   */
  async generateCodeSkeletons(designDocument: SpecDocument): Promise<CodeSkeleton[]> {
    const skeletons: CodeSkeleton[] = [];
    const content = designDocument.content.toLowerCase();

    // Analyser le contenu pour identifier les composants
    const components = this.extractComponents(designDocument.content);
    const models = this.extractModels(designDocument.content);
    const services = this.extractServices(designDocument.content);

    // Générer les squelettes pour les composants
    for (const component of components) {
      skeletons.push(this.generateComponentSkeleton(component, designDocument.featureName));
    }

    // Générer les squelettes pour les modèles
    for (const model of models) {
      skeletons.push(this.generateModelSkeleton(model, designDocument.featureName));
    }

    // Générer les squelettes pour les services
    for (const service of services) {
      skeletons.push(this.generateServiceSkeleton(service, designDocument.featureName));
    }

    // Générer des fichiers de base si aucun composant spécifique n'est trouvé
    if (skeletons.length === 0) {
      skeletons.push(this.generateDefaultSkeleton(designDocument.featureName));
    }

    return skeletons;
  }

  /**
   * Génère la structure de dossiers pour le projet
   * @param designDocument Le document de conception
   * @returns Un tableau de chemins de dossiers à créer
   */
  async generateProjectStructure(designDocument: SpecDocument): Promise<string[]> {
    const structure: string[] = [];
    const featureName = designDocument.featureName.toLowerCase().replace(/\s+/g, '-');

    // Structure de base
    structure.push(`src/`);
    structure.push(`src/components/`);
    structure.push(`src/models/`);
    structure.push(`src/services/`);
    structure.push(`src/utils/`);
    structure.push(`tests/`);
    structure.push(`docs/`);

    // Structure spécifique à la fonctionnalité
    structure.push(`src/features/${featureName}/`);
    structure.push(`src/features/${featureName}/components/`);
    structure.push(`src/features/${featureName}/services/`);
    structure.push(`tests/${featureName}/`);

    return structure;
  }

  /**
   * Extrait les composants mentionnés dans le document de conception
   */
  private extractComponents(content: string): string[] {
    const components: string[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (lowerLine.includes('composant') || lowerLine.includes('component')) {
        const match = line.match(/composant\s+(\w+)|component\s+(\w+)/i);
        if (match) {
          components.push(match[1] || match[2]);
        }
      }
    }

    return [...new Set(components)];
  }

  /**
   * Extrait les modèles mentionnés dans le document de conception
   */
  private extractModels(content: string): string[] {
    const models: string[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (lowerLine.includes('modèle') || lowerLine.includes('model') || 
          lowerLine.includes('entité') || lowerLine.includes('entity')) {
        const match = line.match(/modèle\s+(\w+)|model\s+(\w+)|entité\s+(\w+)|entity\s+(\w+)/i);
        if (match) {
          models.push(match[1] || match[2] || match[3] || match[4]);
        }
      }
    }

    return [...new Set(models)];
  }

  /**
   * Extrait les services mentionnés dans le document de conception
   */
  private extractServices(content: string): string[] {
    const services: string[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (lowerLine.includes('service')) {
        const match = line.match(/service\s+(\w+)/i);
        if (match) {
          services.push(match[1]);
        }
      }
    }

    return [...new Set(services)];
  }

  /**
   * Génère un squelette de composant
   */
  private generateComponentSkeleton(componentName: string, featureName: string): CodeSkeleton {
    const className = this.capitalize(componentName);
    const content = `/**
 * Composant ${className} pour la fonctionnalité ${featureName}
 */
export class ${className}Component {
  constructor() {
    // TODO: Initialiser le composant
  }

  /**
   * Méthode principale du composant
   */
  public render(): void {
    // TODO: Implémenter la logique de rendu
  }

  /**
   * Méthode de nettoyage
   */
  public destroy(): void {
    // TODO: Nettoyer les ressources
  }
}`;

    return {
      filePath: `src/components/${className}Component.ts`,
      content,
      language: 'typescript',
      description: `Composant ${className} pour ${featureName}`
    };
  }

  /**
   * Génère un squelette de modèle
   */
  private generateModelSkeleton(modelName: string, featureName: string): CodeSkeleton {
    const className = this.capitalize(modelName);
    const content = `/**
 * Modèle ${className} pour la fonctionnalité ${featureName}
 */
export interface ${className} {
  id: string;
  // TODO: Ajouter les propriétés du modèle
}

/**
 * Classe de gestion du modèle ${className}
 */
export class ${className}Manager {
  /**
   * Crée une nouvelle instance de ${className}
   */
  public create(data: Partial<${className}>): ${className} {
    // TODO: Implémenter la création
    throw new Error('Not implemented');
  }

  /**
   * Met à jour une instance de ${className}
   */
  public update(id: string, data: Partial<${className}>): ${className} {
    // TODO: Implémenter la mise à jour
    throw new Error('Not implemented');
  }

  /**
   * Supprime une instance de ${className}
   */
  public delete(id: string): boolean {
    // TODO: Implémenter la suppression
    throw new Error('Not implemented');
  }
}`;

    return {
      filePath: `src/models/${className}.ts`,
      content,
      language: 'typescript',
      description: `Modèle ${className} pour ${featureName}`
    };
  }

  /**
   * Génère un squelette de service
   */
  private generateServiceSkeleton(serviceName: string, featureName: string): CodeSkeleton {
    const className = this.capitalize(serviceName);
    const content = `/**
 * Service ${className} pour la fonctionnalité ${featureName}
 */
export class ${className}Service {
  constructor() {
    // TODO: Initialiser le service
  }

  /**
   * Méthode principale du service
   */
  public async execute(): Promise<void> {
    // TODO: Implémenter la logique métier
  }

  /**
   * Méthode de validation
   */
  public validate(data: any): boolean {
    // TODO: Implémenter la validation
    return true;
  }
}`;

    return {
      filePath: `src/services/${className}Service.ts`,
      content,
      language: 'typescript',
      description: `Service ${className} pour ${featureName}`
    };
  }

  /**
   * Génère un squelette par défaut
   */
  private generateDefaultSkeleton(featureName: string): CodeSkeleton {
    const className = this.capitalize(featureName.replace(/\s+/g, ''));
    const content = `/**
 * Implémentation principale pour la fonctionnalité ${featureName}
 */
export class ${className} {
  constructor() {
    // TODO: Initialiser la fonctionnalité
  }

  /**
   * Point d'entrée principal
   */
  public async start(): Promise<void> {
    // TODO: Implémenter la logique principale
  }
}`;

    return {
      filePath: `src/${className}.ts`,
      content,
      language: 'typescript',
      description: `Implémentation principale pour ${featureName}`
    };
  }

  /**
   * Met en forme un nom avec la première lettre en majuscule
   */
  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}
