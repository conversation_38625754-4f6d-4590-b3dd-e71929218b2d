import { EarsCriterion } from '../../domain/entities/EarsCriterion.js';

export interface DesignComponent {
  name: string;
  type: string;
  description: string;
  dependencies: string[];
}

export interface StructureValidation {
  isValid: boolean;
  missingElements: string[];
  suggestions: string[];
}

export interface SpecParserPort {
  parseRequirements(filePath: string): Promise<EarsCriterion[]>;
  parseDesign(filePath: string): Promise<DesignComponent[]>;
  validateDocumentStructure(filePath: string, templatePath: string): Promise<StructureValidation>;
}
