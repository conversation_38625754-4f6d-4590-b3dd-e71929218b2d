/**
 * Entité du domaine représentant les squelettes de code générés.
 * Cette entité représente la structure des fichiers de code à générer
 * à partir des spécifications de conception.
 */
export interface CodeSkeleton {
  /**
   * Le chemin du fichier de code
   */
  filePath: string;
  
  /**
   * Le contenu du squelette de code
   */
  content: string;
  
  /**
   * Le langage de programmation
   */
  language: string;
  
  /**
   * La description du fichier/composant
   */
  description: string;
}
