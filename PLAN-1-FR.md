J'ai élaboré le plan suivant après une exploration et une analyse approfondies de la base de code. Suivez le plan ci-dessous à la lettre. Faites confiance aux fichiers et aux références. Ne revérifiez pas ce qui est écrit dans le plan. Explorez uniquement si cela est absolument nécessaire. Implémentez d'abord tous les changements de fichiers proposés, puis je passerai en revue tous les changements ensemble à la fin.

### Observations

La base de code actuelle est un serveur MCP simple avec toute la logique métier concentrée dans `server.ts`. Il expose 3 prompts pour le workflow de développement piloté par les spécifications. Le code doit être restructuré selon les principes de l'architecture hexagonale tout en maintenant une compatibilité totale avec l'API MCP existante. Le principal défi est de bien séparer les responsabilités sans casser les contrats JSON-RPC actuels.

### Approche

Je vais restructurer la base de code en utilisant l'architecture hexagonale en créant quatre couches distinctes : domaine (logique métier pure), application (cas d'utilisation), infrastructure (adaptateurs externes) et présentation (interface MCP). La stratégie consiste à extraire la logique métier de `server.ts` vers les couches appropriées tout en conservant la même API publique. Chaque prompt MCP deviendra un cas d'utilisation, avec des interfaces (ports) appropriées pour découpler les dépendances. Le refactoring sera effectué de manière incrémentale pour garantir que la compatibilité soit maintenue tout au long du processus.

### Raisonnement

J'ai exploré la structure du dépôt et lu tous les fichiers sources pertinents pour comprendre l'architecture actuelle. La base de code consiste en un serveur MCP simple avec trois prompts pour générer des exigences, des documents de conception et du code. J'ai analysé les dépendances, la logique métier et identifié les points d'intégration clés à préserver lors de la restructuration hexagonale.

## Diagramme Mermaid

sequenceDiagram
    participant Client as Client MCP
    participant Presentation as Couche Présentation<br/>(McpServerFactory)
    participant Application as Couche Application<br/>(Cas d'utilisation)
    participant Domain as Couche Domaine<br/>(Entités)
    participant Infrastructure as Couche Infrastructure<br/>(Adaptateur MCP)

    Client->>Presentation: Requête Prompt MCP<br/>(generate-requirements)
    Presentation->>Application: Exécute le cas d'utilisation<br/>(GenerateRequirementsUseCase)
    Application->>Domain: Crée des entités ChatMessage
    Domain-->>Application: Retourne des objets ChatMessage
    Application->>Infrastructure: Construit la réponse MCP<br/>(via PromptBuilderPort)
    Infrastructure-->>Application: Réponse au format MCP
    Application-->>Presentation: Résultat du cas d'utilisation
    Presentation-->>Client: Réponse Prompt MCP

    Note over Presentation,Infrastructure: Séparation claire des responsabilités<br/>avec inversion de dépendance
    Note over Domain: Logique métier pure<br/>sans dépendances externes
    Note over Infrastructure: Intégration SDK MCP<br/>implémente les ports de l'application

## Changements de fichiers proposés

### src\domain(NOUVEAU)

Créer le dossier de la couche domaine pour contenir la logique métier pure, les entités et les objets de valeur sans dépendances externes.

### src\domain\index.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la couche domaine afin d'exposer toutes les entités et objets de valeur du domaine.

### src\domain\entities(NOUVEAU)

Créer le dossier entities pour contenir les entités du domaine comme ChatMessage et PromptDefinition.

### src\domain\entities\ChatMessage.ts(NOUVEAU)

Créer l'entité ChatMessage pour représenter la structure des messages de chat utilisés dans les prompts MCP. Ce sera un simple objet de valeur avec des propriétés comme `role`, `content` avec une typage appropriée mais sans dépendances externes.

### src\domain\entities\PromptDefinition.ts(NOUVEAU)

Créer l'entité PromptDefinition pour encapsuler les métadonnées du prompt comme le nom, le titre, la description et le schéma des arguments. Cela représente le concept métier central de ce qu'est un prompt, indépendant de l'implémentation du SDK MCP.

### src\domain\entities\index.ts(NOUVEAU)

Références : 

- src\domain\entities\ChatMessage.ts(NOUVEAU)
- src\domain\entities\PromptDefinition.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour les entités du domaine afin d'exporter ChatMessage et PromptDefinition.

### src\application(NOUVEAU)

Créer le dossier de la couche application pour contenir les cas d'utilisation, les ports (interfaces) et les services d'application qui orchestrent la logique métier.

### src\application\index.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la couche application afin d'exposer les cas d'utilisation et les ports.

### src\application\ports(NOUVEAU)

Créer le dossier ports pour contenir les définitions d'interfaces qui découplent la couche application des préoccupations d'infrastructure.

### src\application\ports\PromptBuilderPort.ts(NOUVEAU)

Créer l'interface PromptBuilderPort qui définit le contrat pour construire les réponses de prompt MCP. Cette interface sera implémentée par les adaptateurs d'infrastructure et utilisée par les cas d'utilisation de l'application pour rester découplés des spécificités du SDK MCP.

### src\application\ports\index.ts(NOUVEAU)

Références : 

- src\application\ports\PromptBuilderPort.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour les ports de l'application afin d'exporter l'interface PromptBuilderPort.

### src\application\use-cases(NOUVEAU)

Créer le dossier use-cases pour contenir les cas d'utilisation de l'application qui implémentent les workflows métier.

### src\application\use-cases\GenerateRequirementsUseCase.ts(NOUVEAU)

Références : 

- src\server.ts(MODIFIER)
- src\application\ports\PromptBuilderPort.ts(NOUVEAU)
- src\domain\entities\ChatMessage.ts(NOUVEAU)

Créer GenerateRequirementsUseCase qui encapsule la logique métier pour générer des documents d'exigences. Ce cas d'utilisation prendra en entrée les exigences et retournera un tableau de ChatMessage, en utilisant le PromptBuilderPort pour toute interaction externe. La logique sera extraite du prompt `generate-requirements` actuel dans `src/server.ts`.

### src\application\use-cases\GenerateDesignFromRequirementsUseCase.ts(NOUVEAU)

Références : 

- src\server.ts(MODIFIER)
- src\application\ports\PromptBuilderPort.ts(NOUVEAU)
- src\domain\entities\ChatMessage.ts(NOUVEAU)

Créer GenerateDesignFromRequirementsUseCase qui gère la logique métier pour générer des documents de conception à partir des exigences. Ce cas d'utilisation retournera le tableau approprié de ChatMessage pour le workflow de génération de conception. La logique sera extraite du prompt `generate-design-from-requirements` actuel dans `src/server.ts`.

### src\application\use-cases\GenerateCodeFromDesignUseCase.ts(NOUVEAU)

Références : 

- src\server.ts(MODIFIER)
- src\application\ports\PromptBuilderPort.ts(NOUVEAU)
- src\domain\entities\ChatMessage.ts(NOUVEAU)

Créer GenerateCodeFromDesignUseCase qui implémente la logique métier pour générer du code à partir des documents de conception. Ce cas d'utilisation retournera le tableau approprié de ChatMessage pour la génération de code. La logique sera extraite du prompt `generate-code-from-design` actuel dans `src/server.ts`.

### src\application\use-cases\index.ts(NOUVEAU)

Références : 

- src\application\use-cases\GenerateRequirementsUseCase.ts(NOUVEAU)
- src\application\use-cases\GenerateDesignFromRequirementsUseCase.ts(NOUVEAU)
- src\application\use-cases\GenerateCodeFromDesignUseCase.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour les cas d'utilisation de l'application afin d'exporter les trois classes de cas d'utilisation.

### src\infrastructure(NOUVEAU)

Créer le dossier de la couche infrastructure pour contenir les adaptateurs qui implémentent les ports et gèrent les dépendances externes comme le SDK MCP, le système de fichiers, etc.

### src\infrastructure\index.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la couche infrastructure afin d'exposer les adaptateurs et les implémentations.

### src\infrastructure\mcp(NOUVEAU)

Créer le dossier infrastructure MCP pour contenir les adaptateurs qui implémentent l'intégration SDK MCP.

### src\infrastructure\mcp\McpPromptAdapter.ts(NOUVEAU)

Références : 

- src\application\ports\PromptBuilderPort.ts(NOUVEAU)
- src\domain\entities\ChatMessage.ts(NOUVEAU)

Créer McpPromptAdapter qui implémente l'interface PromptBuilderPort en utilisant le SDK MCP. Cet adaptateur gérera la traduction entre les objets ChatMessage du domaine et le format de réponse du prompt du SDK MCP, en maintenant la compatibilité avec les contrats API MCP existants.

### src\infrastructure\mcp\index.ts(NOUVEAU)

Références : 

- src\infrastructure\mcp\McpPromptAdapter.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour l'infrastructure MCP afin d'exporter McpPromptAdapter.

### src\presentation(NOUVEAU)

Créer le dossier de la couche présentation pour contenir les points d'entrée et les adaptateurs d'interface utilisateur comme la factory du serveur MCP.

### src\presentation\index.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la couche présentation afin d'exposer la factory du serveur MCP et autres composants de présentation.

### src\presentation\mcp(NOUVEAU)

Créer le dossier présentation MCP pour contenir la logique de présentation spécifique MCP comme la factory du serveur.

### src\presentation\mcp\McpServerFactory.ts(NOUVEAU)

Références : 

- src\server.ts(MODIFIER)
- src\application\use-cases\index.ts(NOUVEAU)
- src\infrastructure\mcp\McpPromptAdapter.ts(NOUVEAU)

Créer McpServerFactory qui remplace la fonction `createServer` de `src/server.ts`. Cette factory instanciera tous les cas d'utilisation, les connectera aux adaptateurs d'infrastructure et les enregistrera auprès du serveur MCP. Elle doit conserver exactement la même signature publique `(): McpServer` pour garantir la compatibilité avec les points d'entrée existants dans `src/index.ts` et `src/streamableHttp.ts`.

### src\presentation\mcp\index.ts(NOUVEAU)

Références : 

- src\presentation\mcp\McpServerFactory.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la présentation MCP afin d'exporter McpServerFactory.

### src\server.ts(MODIFIER)

Références : 

- src\presentation\mcp\McpServerFactory.ts(NOUVEAU)
- src\index.ts(MODIFIER)
- src\streamableHttp.ts(MODIFIER)

Refactoriser le fichier server.ts existant pour utiliser la nouvelle architecture hexagonale. Remplacer l'implémentation actuelle de la fonction `createServer` pour déléguer à la nouvelle McpServerFactory de la couche présentation. Retirer toute la logique métier qui a été déplacée dans les cas d'utilisation, en ne gardant que la délégation à la factory. Maintenir la même signature d'export pour garantir la compatibilité avec `src/index.ts` et `src/streamableHttp.ts`.

### src\index.ts(MODIFIER)

Références : 

- src\server.ts(MODIFIER)

Mettre à jour le chemin d'import pour utiliser la fonction `createServer` refactorisée depuis `src/server.ts`. Aucun autre changement n'est nécessaire puisque la signature de la fonction reste la même. Vérifier que l'intégration du transport STDIO continue de fonctionner comme prévu.

### src\streamableHttp.ts(MODIFIER)

Références : 

- src\server.ts(MODIFIER)

Mettre à jour le chemin d'import pour utiliser la fonction `createServer` refactorisée depuis `src/server.ts`. Aucun autre changement n'est nécessaire puisque la signature de la fonction reste la même. Vérifier que l'intégration du transport HTTP continue de fonctionner comme prévu.