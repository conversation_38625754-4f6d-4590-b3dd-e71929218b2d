I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current codebase is a simple MCP server with 3 prompts for spec-driven development. The `.claude/` directory contains rich command specifications and French templates that follow EARS format. There are existing validation agents that can be leveraged for the new `/spec-validate` command. The user wants to add two new commands that extend the existing workflow while maintaining compatibility. The hexagonal architecture needs to be implemented alongside the new functionality.

### Approach

I'll implement the two new commands by extending the existing MCP server with hexagonal architecture. The `/spec-create-full` command will orchestrate the complete workflow (requirements → design → code skeletons) in a single prompt, while `/spec-validate` will leverage existing validation agents to check spec-code consistency. Both commands will use the existing French templates and maintain full compatibility with the current MCP API. The implementation will introduce clean separation of concerns through domain entities, application use cases, and infrastructure adapters while keeping the same `createServer()` signature.

### Reasoning

I explored the repository structure and analyzed the current MCP server implementation. I read the existing prompts, French templates, command specifications, and validation agents to understand the workflow patterns. I confirmed that the codebase is still in its original flat structure and identified the integration points needed for the new functionality. I also examined the transport layers (stdio and HTTP) to ensure compatibility is maintained.

## Mermaid Diagram

sequenceDiagram
    participant Client as MCP Client
    participant Presentation as Presentation Layer<br/>(McpServerFactory)
    participant Application as Application Layer<br/>(Use Cases)
    participant Infrastructure as Infrastructure Layer<br/>(Adapters)
    participant Templates as .claude/templates/
    participant Agents as .claude/agents/

    Note over Client,Agents: /spec-create-full workflow
    Client->>Presentation: MCP Prompt Request<br/>(spec-create-full)
    Presentation->>Application: Execute CreateFullSpecUseCase
    Application->>Infrastructure: Load templates<br/>(TemplateProviderPort)
    Infrastructure->>Templates: Read requirements-template.md<br/>design-template.md
    Templates-->>Infrastructure: Template content
    Infrastructure-->>Application: Loaded templates
    Application->>Infrastructure: Generate code skeletons<br/>(CodeGeneratorPort)
    Infrastructure-->>Application: CodeSkeleton entities
    Application-->>Presentation: Complete spec package
    Presentation-->>Client: MCP Response with<br/>requirements + design + code

    Note over Client,Agents: /spec-validate workflow  
    Client->>Presentation: MCP Prompt Request<br/>(spec-validate)
    Presentation->>Application: Execute ValidateSpecUseCase
    Application->>Infrastructure: Validate documents<br/>(ValidationAgentPort)
    Infrastructure->>Agents: Apply validation logic<br/>from spec-*-validator.md
    Agents-->>Infrastructure: Validation results
    Infrastructure-->>Application: ValidationReport entities
    Application-->>Presentation: Consolidated validation
    Presentation-->>Client: MCP Response with<br/>validation status + issues

## Proposed File Changes

### src\domain(NEW)

Create the domain layer directory to contain pure business logic entities and value objects without external dependencies.

### src\domain\entities(NEW)

Create entities directory to contain domain entities for the spec-driven development workflow.

### src\domain\entities\SpecDocument.ts(NEW)

Create SpecDocument entity to represent specification documents (requirements.md, design.md) with properties like `type`, `content`, `featureName`, and `status`. This entity encapsulates the core business concept of a specification document independent of file system concerns.

### src\domain\entities\ValidationReport.ts(NEW)

Create ValidationReport entity to represent validation results with properties like `status` (PASS/NEEDS_IMPROVEMENT/MAJOR_ISSUES), `issues`, `suggestions`, and `strengths`. This encapsulates validation outcomes from the validation agents.

### src\domain\entities\CodeSkeleton.ts(NEW)

Create CodeSkeleton entity to represent generated code skeletons with properties like `filePath`, `content`, `language`, and `description`. This represents the structure of code files to be generated based on design specifications.

### src\domain\entities\index.ts(NEW)

References: 

- src\domain\entities\SpecDocument.ts(NEW)
- src\domain\entities\ValidationReport.ts(NEW)
- src\domain\entities\CodeSkeleton.ts(NEW)

Create barrel export file for domain entities to export SpecDocument, ValidationReport, and CodeSkeleton entities.

### src\domain\index.ts(NEW)

References: 

- src\domain\entities\index.ts(NEW)

Create barrel export file for the domain layer to expose all domain entities.

### src\application(NEW)

Create the application layer directory to contain use cases, ports, and application services.

### src\application\ports(NEW)

Create ports directory to contain interface definitions that decouple the application layer from infrastructure concerns.

### src\application\ports\TemplateProviderPort.ts(NEW)

References: 

- .claude\templates\requirements-template.md
- .claude\templates\design-template.md

Create TemplateProviderPort interface that defines the contract for loading template files. This interface will have methods like `getTemplate(templateName: string): Promise<string>` to load templates from `.claude/templates/` directory. This port will be implemented by infrastructure adapters.

### src\application\ports\ValidationAgentPort.ts(NEW)

References: 

- .claude\agents\spec-requirements-validator.md
- .claude\agents\spec-design-validator.md

Create ValidationAgentPort interface that defines the contract for validation services. This interface will have methods like `validateRequirements(document: SpecDocument): Promise<ValidationReport>` and `validateDesign(document: SpecDocument): Promise<ValidationReport>` to interact with the existing validation agents from `.claude/agents/`.

### src\application\ports\CodeGeneratorPort.ts(NEW)

Create CodeGeneratorPort interface that defines the contract for generating code skeletons. This interface will have methods like `generateCodeSkeletons(designDocument: SpecDocument): Promise<CodeSkeleton[]>` to create code structure based on design specifications.

### src\application\ports\index.ts(NEW)

References: 

- src\application\ports\TemplateProviderPort.ts(NEW)
- src\application\ports\ValidationAgentPort.ts(NEW)
- src\application\ports\CodeGeneratorPort.ts(NEW)

Create barrel export file for application ports to export TemplateProviderPort, ValidationAgentPort, and CodeGeneratorPort interfaces.

### src\application\use-cases(NEW)

Create use-cases directory to contain application use cases that implement the business workflows.

### src\application\use-cases\CreateFullSpecUseCase.ts(NEW)

References: 

- src\application\ports\TemplateProviderPort.ts(NEW)
- src\application\ports\CodeGeneratorPort.ts(NEW)
- src\domain\entities\SpecDocument.ts(NEW)
- src\domain\entities\CodeSkeleton.ts(NEW)

Create CreateFullSpecUseCase that orchestrates the complete spec-driven development workflow. This use case will: 1) Generate requirements.md using the requirements template, 2) Generate design.md using the design template, 3) Generate code skeletons based on the design. It will use TemplateProviderPort to load templates and CodeGeneratorPort to generate skeletons. The use case will return a comprehensive response with all generated documents and code structure.

### src\application\use-cases\ValidateSpecUseCase.ts(NEW)

References: 

- src\application\ports\ValidationAgentPort.ts(NEW)
- src\domain\entities\SpecDocument.ts(NEW)
- src\domain\entities\ValidationReport.ts(NEW)

Create ValidateSpecUseCase that validates the consistency between specifications and code. This use case will: 1) Load existing requirements.md and design.md documents, 2) Use ValidationAgentPort to validate each document, 3) Perform cross-validation between specs and existing code, 4) Generate a comprehensive validation report. It will leverage the existing validation agents from `.claude/agents/` through the ValidationAgentPort interface.

### src\application\use-cases\index.ts(NEW)

References: 

- src\application\use-cases\CreateFullSpecUseCase.ts(NEW)
- src\application\use-cases\ValidateSpecUseCase.ts(NEW)

Create barrel export file for application use cases to export CreateFullSpecUseCase and ValidateSpecUseCase.

### src\application\index.ts(NEW)

References: 

- src\application\use-cases\index.ts(NEW)
- src\application\ports\index.ts(NEW)

Create barrel export file for the application layer to expose use cases and ports.

### src\infrastructure(NEW)

Create the infrastructure layer directory to contain adapters that implement ports and handle external dependencies.

### src\infrastructure\adapters(NEW)

Create adapters directory to contain concrete implementations of the application ports.

### src\infrastructure\adapters\FileSystemTemplateProvider.ts(NEW)

References: 

- src\application\ports\TemplateProviderPort.ts(NEW)
- .claude\templates\requirements-template.md
- .claude\templates\design-template.md

Create FileSystemTemplateProvider that implements TemplateProviderPort interface. This adapter will read template files from the `.claude/templates/` directory using Node.js file system APIs. It will handle loading `requirements-template.md`, `design-template.md`, and other templates as needed. Include error handling for missing templates and proper path resolution.

### src\infrastructure\adapters\McpValidationAdapter.ts(NEW)

References: 

- src\application\ports\ValidationAgentPort.ts(NEW)
- src\domain\entities\ValidationReport.ts(NEW)
- .claude\agents\spec-requirements-validator.md

Create McpValidationAdapter that implements ValidationAgentPort interface. This adapter will simulate the behavior of the validation agents from `.claude/agents/` by implementing the validation logic described in `spec-requirements-validator.md` and `spec-design-validator.md`. It will validate document structure, content quality, and alignment with templates, returning ValidationReport entities with appropriate status and feedback.

### src\infrastructure\adapters\BasicCodeGenerator.ts(NEW)

References: 

- src\application\ports\CodeGeneratorPort.ts(NEW)
- src\domain\entities\CodeSkeleton.ts(NEW)
- .claude\templates\tech-template.md
- .claude\templates\structure-template.md

Create BasicCodeGenerator that implements CodeGeneratorPort interface. This adapter will generate basic code skeletons based on design documents. It will parse design content to identify components, models, and services, then generate appropriate file structures and basic class/function templates. The generator will use the `tech-template.md` and `structure-template.md` to understand the project's technology stack and organization patterns.

### src\infrastructure\adapters\index.ts(NEW)

References: 

- src\infrastructure\adapters\FileSystemTemplateProvider.ts(NEW)
- src\infrastructure\adapters\McpValidationAdapter.ts(NEW)
- src\infrastructure\adapters\BasicCodeGenerator.ts(NEW)

Create barrel export file for infrastructure adapters to export FileSystemTemplateProvider, McpValidationAdapter, and BasicCodeGenerator.

### src\infrastructure\index.ts(NEW)

References: 

- src\infrastructure\adapters\index.ts(NEW)

Create barrel export file for the infrastructure layer to expose adapters and implementations.

### src\presentation(NEW)

Create the presentation layer directory to contain entry points and user interface adapters.

### src\presentation\mcp(NEW)

Create MCP presentation directory to contain MCP-specific presentation logic.

### src\presentation\mcp\McpPromptHandlers.ts(NEW)

References: 

- src\server.ts(MODIFY)
- src\application\use-cases\index.ts(NEW)

Create McpPromptHandlers that contains the prompt handler functions for all MCP prompts. This will include handlers for the existing prompts (generate-requirements, generate-design-from-requirements, generate-code-from-design) and the two new prompts (spec-create-full, spec-validate). Each handler will instantiate the appropriate use case, execute it, and format the response as MCP chat messages. The handlers will maintain the same response format as the current implementation in `src/server.ts`.

### src\presentation\mcp\McpServerFactory.ts(NEW)

References: 

- src\server.ts(MODIFY)
- src\infrastructure\adapters\index.ts(NEW)
- src\application\use-cases\index.ts(NEW)
- src\presentation\mcp\McpPromptHandlers.ts(NEW)

Create McpServerFactory that replaces the `createServer` function from `src/server.ts`. This factory will: 1) Instantiate all infrastructure adapters (FileSystemTemplateProvider, McpValidationAdapter, BasicCodeGenerator), 2) Create use case instances with injected dependencies, 3) Register all 5 MCP prompts (3 existing + 2 new) with the MCP server, 4) Wire the prompt handlers to the appropriate use cases. The factory must maintain the exact same public API signature `(): McpServer` to ensure compatibility.

### src\presentation\mcp\index.ts(NEW)

References: 

- src\presentation\mcp\McpServerFactory.ts(NEW)
- src\presentation\mcp\McpPromptHandlers.ts(NEW)

Create barrel export file for MCP presentation to export McpServerFactory and McpPromptHandlers.

### src\presentation\index.ts(NEW)

References: 

- src\presentation\mcp\index.ts(NEW)

Create barrel export file for the presentation layer to expose the MCP server factory.

### src\server.ts(MODIFY)

References: 

- src\presentation\mcp\McpServerFactory.ts(NEW)
- src\index.ts
- src\streamableHttp.ts

Refactor the existing server.ts to use the new hexagonal architecture. Replace the current `createServer` function implementation to delegate to the new `McpServerFactory` from the presentation layer. Remove all business logic and prompt registration code that has been moved to the presentation and application layers. Keep only a simple delegation: `export function createServer(): McpServer { return McpServerFactory.create(); }`. Maintain the same export signature to ensure compatibility with `src/index.ts` and `src/streamableHttp.ts`.

### .claude\commands\spec-create-full.md(NEW)

References: 

- .claude\commands\spec-create.md
- .claude\templates\requirements-template.md
- .claude\templates\design-template.md

Create command specification for `/spec-create-full` following the same format as existing commands in `.claude/commands/`. Document the command usage, philosophy, and workflow. Specify that this command generates requirements.md, design.md, and code skeletons in a single operation. Include argument schema (feature name and description), expected outputs, and integration with existing templates. Reference the templates from `.claude/templates/` that will be used as base for generation.

### .claude\commands\spec-validate.md(NEW)

References: 

- .claude\commands\spec-status.md
- .claude\agents\spec-requirements-validator.md
- .claude\agents\spec-design-validator.md

Create command specification for `/spec-validate` following the same format as existing commands. Document the command usage for validating consistency between specifications and code. Specify that this command leverages existing validation agents from `.claude/agents/` to check requirements.md and design.md quality, then performs cross-validation with existing code. Include expected validation criteria, output format (PASS/NEEDS_IMPROVEMENT/MAJOR_ISSUES), and integration points with the validation agents.