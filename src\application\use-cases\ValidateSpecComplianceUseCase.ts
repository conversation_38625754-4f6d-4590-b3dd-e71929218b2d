import { SpecParserPort } from '../ports/SpecParserPort.js';
import { CodeAnalyzerPort } from '../ports/CodeAnalyzerPort.js';
import { FileSystemPort } from '../ports/FileSystemPort.js';
import { ComplianceReport } from '../../domain/entities/ComplianceReport.js';

export class ValidateSpecComplianceUseCase {
  constructor(
    private specParser: SpecParserPort,
    private codeAnalyzer: CodeAnalyzerPort,
    private fileSystem: FileSystemPort
  ) {}

  async execute(): Promise<ComplianceReport> {
    try {
      // 1. Vérifier l'existence des fichiers de spécification
      const requirementsPath = 'specs/requirements.md';
      const designPath = 'specs/design.md';
      
      const requirementsExists = await this.fileSystem.fileExists(requirementsPath);
      if (!requirementsExists) {
        throw new Error(`Fichier de spécifications manquant: ${requirementsPath}`);
      }

      // 2. Extraire les critères EARS des exigences
      const earsCriteria = await this.specParser.parseRequirements(requirementsPath);
      
      if (earsCriteria.length === 0) {
        return {
          overallStatus: 'MAJOR_ISSUES',
          totalCriteria: 0,
          implementedCriteria: 0,
          issues: [],
          suggestions: ['Aucun critère EARS trouvé dans les spécifications. Vérifiez le format des exigences.'],
          strengths: [],
          coveragePercentage: 0,
          generatedAt: new Date()
        };
      }

      // 3. Analyser le code pour trouver des preuves d'implémentation
      const codeEvidence = await this.codeAnalyzer.analyzeCodebase('src');

      // 4. Identifier les problèmes de conformité
      const complianceIssues = await this.codeAnalyzer.findEvidenceForCriteria(earsCriteria, codeEvidence);

      // 5. Générer le rapport de conformité
      const complianceReport = await this.codeAnalyzer.generateComplianceReport(earsCriteria, complianceIssues);

      return complianceReport;

    } catch (error) {
      // Gestion d'erreur avec rapport d'échec
      return {
        overallStatus: 'MAJOR_ISSUES',
        totalCriteria: 0,
        implementedCriteria: 0,
        issues: [],
        suggestions: [`Erreur lors de la validation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`],
        strengths: [],
        coveragePercentage: 0,
        generatedAt: new Date()
      };
    }
  }
}
