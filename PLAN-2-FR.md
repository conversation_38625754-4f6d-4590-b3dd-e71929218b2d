J'ai créé le plan suivant après une exploration et une analyse approfondies de la base de code. Suivez ce plan à la lettre. Faites confiance aux fichiers et aux références. Ne revérifiez pas ce qui est écrit dans le plan. Explorez uniquement si absolument nécessaire. Implémentez d'abord tous les changements proposés dans les fichiers, puis je passerai en revue toutes les modifications à la fin.

### Observations

La base de code actuelle est un serveur MCP simple avec 3 prompts pour le développement piloté par les spécifications. Le dossier `.claude/` contient des spécifications de commandes riches et des modèles français suivant le format EARS. Il existe déjà des agents de validation qui peuvent être utilisés pour la nouvelle commande `/spec-validate`. L'utilisateur souhaite ajouter deux nouvelles commandes qui étendent le workflow existant tout en maintenant la compatibilité. L'architecture hexagonale doit être mise en place en parallèle des nouvelles fonctionnalités.

### Approche

J'implémenterai les deux nouvelles commandes en étendant le serveur MCP existant avec une architecture hexagonale. La commande `/spec-create-full` orchestrera le workflow complet (exigences → conception → squelettes de code) en une seule invite, tandis que `/spec-validate` utilisera les agents de validation existants pour vérifier la cohérence entre les spécifications et le code. Les deux commandes utiliseront les modèles français existants et maintiendront une compatibilité totale avec l'API MCP actuelle. L'implémentation introduira une séparation claire des responsabilités via des entités métier, des cas d'utilisation applicatifs et des adaptateurs d'infrastructure, tout en conservant la même signature `createServer()`.

### Raisonnement

J'ai exploré la structure du dépôt et analysé l'implémentation actuelle du serveur MCP. J'ai lu les prompts existants, les modèles français, les spécifications de commandes et les agents de validation pour comprendre les schémas de workflow. J'ai confirmé que la base de code est toujours dans sa structure plate d'origine et identifié les points d'intégration nécessaires pour les nouvelles fonctionnalités. J'ai également examiné les couches de transport (stdio et HTTP) pour garantir la compatibilité.

## Diagramme Mermaid

sequenceDiagram
    participant Client as Client MCP
    participant Presentation as Couche Présentation<br/>(McpServerFactory)
    participant Application as Couche Application<br/>(Cas d'utilisation)
    participant Infrastructure as Couche Infrastructure<br/>(Adaptateurs)
    participant Templates as .claude/templates/
    participant Agents as .claude/agents/

    Note over Client,Agents: workflow /spec-create-full
    Client->>Presentation: Requête MCP Prompt<br/>(spec-create-full)
    Presentation->>Application: Exécute CreateFullSpecUseCase
    Application->>Infrastructure: Charge les modèles<br/>(TemplateProviderPort)
    Infrastructure->>Templates: Lit requirements-template.md<br/>design-template.md
    Templates-->>Infrastructure: Contenu du modèle
    Infrastructure-->>Application: Modèles chargés
    Application->>Infrastructure: Génère les squelettes de code<br/>(CodeGeneratorPort)
    Infrastructure-->>Application: Entités CodeSkeleton
    Application-->>Presentation: Package de spécification complet
    Presentation-->>Client: Réponse MCP avec<br/>exigences + conception + code

    Note over Client,Agents: workflow /spec-validate  
    Client->>Presentation: Requête MCP Prompt<br/>(spec-validate)
    Presentation->>Application: Exécute ValidateSpecUseCase
    Application->>Infrastructure: Valide les documents<br/>(ValidationAgentPort)
    Infrastructure->>Agents: Applique la logique de validation<br/>depuis spec-*-validator.md
    Agents-->>Infrastructure: Résultats de validation
    Infrastructure-->>Application: Entités ValidationReport
    Application-->>Presentation: Validation consolidée
    Presentation-->>Client: Réponse MCP avec<br/>statut de validation + problèmes

## Modifications de fichiers proposées

### src\domain(NOUVEAU)

Créer le dossier du domaine pour contenir les entités métier pures et les objets de valeur sans dépendances externes.

### src\domain\entities(NOUVEAU)

Créer le dossier entities pour contenir les entités métier du workflow piloté par les spécifications.

### src\domain\entities\SpecDocument.ts(NOUVEAU)

Créer l'entité SpecDocument pour représenter les documents de spécification (requirements.md, design.md) avec les propriétés `type`, `content`, `featureName` et `status`. Cette entité encapsule le concept métier principal d'un document de spécification, indépendant des préoccupations du système de fichiers.

### src\domain\entities\ValidationReport.ts(NOUVEAU)

Créer l'entité ValidationReport pour représenter les résultats de validation avec les propriétés `status` (PASS/BESOIN_AMÉLIORATION/PROBLÈMES_MAJORS), `issues`, `suggestions` et `strengths`. Cela encapsule les résultats des agents de validation.

### src\domain\entities\CodeSkeleton.ts(NOUVEAU)

Créer l'entité CodeSkeleton pour représenter les squelettes de code générés avec les propriétés `filePath`, `content`, `language` et `description`. Cela représente la structure des fichiers de code à générer à partir des spécifications de conception.

### src\domain\entities\index.ts(NOUVEAU)

Références : 

- src\domain\entities\SpecDocument.ts(NOUVEAU)
- src\domain\entities\ValidationReport.ts(NOUVEAU)
- src\domain\entities\CodeSkeleton.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour les entités du domaine afin d'exporter SpecDocument, ValidationReport et CodeSkeleton.

### src\domain\index.ts(NOUVEAU)

Références : 

- src\domain\entities\index.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la couche domaine afin d'exposer toutes les entités métier.

### src\application(NOUVEAU)

Créer le dossier application pour contenir les cas d'utilisation, ports et services applicatifs.

### src\application\ports(NOUVEAU)

Créer le dossier ports pour contenir les définitions d'interfaces qui découplent la couche application des préoccupations d'infrastructure.

### src\application\ports\TemplateProviderPort.ts(NOUVEAU)

Références : 

- .claude\templates\requirements-template.md
- .claude\templates\design-template.md

Créer l'interface TemplateProviderPort qui définit le contrat pour charger les fichiers modèles. Cette interface aura des méthodes comme `getTemplate(templateName: string): Promise<string>` pour charger les modèles depuis le dossier `.claude/templates/`. Ce port sera implémenté par des adaptateurs d'infrastructure.

### src\application\ports\ValidationAgentPort.ts(NOUVEAU)

Références : 

- .claude\agents\spec-requirements-validator.md
- .claude\agents\spec-design-validator.md

Créer l'interface ValidationAgentPort qui définit le contrat pour les services de validation. Cette interface aura des méthodes comme `validateRequirements(document: SpecDocument): Promise<ValidationReport>` et `validateDesign(document: SpecDocument): Promise<ValidationReport>` pour interagir avec les agents de validation existants du dossier `.claude/agents/`.

### src\application\ports\CodeGeneratorPort.ts(NOUVEAU)

Créer l'interface CodeGeneratorPort qui définit le contrat pour la génération de squelettes de code. Cette interface aura des méthodes comme `generateCodeSkeletons(designDocument: SpecDocument): Promise<CodeSkeleton[]>` pour créer la structure du code à partir des spécifications de conception.

### src\application\ports\index.ts(NOUVEAU)

Références : 

- src\application\ports\TemplateProviderPort.ts(NOUVEAU)
- src\application\ports\ValidationAgentPort.ts(NOUVEAU)
- src\application\ports\CodeGeneratorPort.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour les ports applicatifs afin d'exporter TemplateProviderPort, ValidationAgentPort et CodeGeneratorPort.

### src\application\use-cases(NOUVEAU)

Créer le dossier use-cases pour contenir les cas d'utilisation qui implémentent les workflows métier.

### src\application\use-cases\CreateFullSpecUseCase.ts(NOUVEAU)

Références : 

- src\application\ports\TemplateProviderPort.ts(NOUVEAU)
- src\application\ports\CodeGeneratorPort.ts(NOUVEAU)
- src\domain\entities\SpecDocument.ts(NOUVEAU)
- src\domain\entities\CodeSkeleton.ts(NOUVEAU)

Créer CreateFullSpecUseCase qui orchestre le workflow complet piloté par les spécifications. Ce cas d'utilisation : 1) Génère requirements.md à partir du modèle d'exigences, 2) Génère design.md à partir du modèle de conception, 3) Génère les squelettes de code à partir de la conception. Il utilisera TemplateProviderPort pour charger les modèles et CodeGeneratorPort pour générer les squelettes. Le cas d'utilisation retournera une réponse complète avec tous les documents générés et la structure du code.

### src\application\use-cases\ValidateSpecUseCase.ts(NOUVEAU)

Références : 

- src\application\ports\ValidationAgentPort.ts(NOUVEAU)
- src\domain\entities\SpecDocument.ts(NOUVEAU)
- src\domain\entities\ValidationReport.ts(NOUVEAU)

Créer ValidateSpecUseCase qui valide la cohérence entre les spécifications et le code. Ce cas d'utilisation : 1) Charge les documents requirements.md et design.md existants, 2) Utilise ValidationAgentPort pour valider chaque document, 3) Effectue une validation croisée entre les spécifications et le code existant, 4) Génère un rapport de validation complet. Il s'appuiera sur les agents de validation existants du dossier `.claude/agents/` via l'interface ValidationAgentPort.

### src\application\use-cases\index.ts(NOUVEAU)

Références : 

- src\application\use-cases\CreateFullSpecUseCase.ts(NOUVEAU)
- src\application\use-cases\ValidateSpecUseCase.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour les cas d'utilisation afin d'exporter CreateFullSpecUseCase et ValidateSpecUseCase.

### src\application\index.ts(NOUVEAU)

Références : 

- src\application\use-cases\index.ts(NOUVEAU)
- src\application\ports\index.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la couche application afin d'exposer les cas d'utilisation et les ports.

### src\infrastructure(NOUVEAU)

Créer le dossier infrastructure pour contenir les adaptateurs qui implémentent les ports et gèrent les dépendances externes.

### src\infrastructure\adapters(NOUVEAU)

Créer le dossier adapters pour contenir les implémentations concrètes des ports applicatifs.

### src\infrastructure\adapters\FileSystemTemplateProvider.ts(NOUVEAU)

Références : 

- src\application\ports\TemplateProviderPort.ts(NOUVEAU)
- .claude\templates\requirements-template.md
- .claude\templates\design-template.md

Créer FileSystemTemplateProvider qui implémente l'interface TemplateProviderPort. Cet adaptateur lira les fichiers modèles du dossier `.claude/templates/` en utilisant les API du système de fichiers Node.js. Il gérera le chargement de `requirements-template.md`, `design-template.md` et autres modèles si nécessaire. Inclure la gestion des erreurs pour les modèles manquants et une résolution correcte des chemins.

### src\infrastructure\adapters\McpValidationAdapter.ts(NOUVEAU)

Références : 

- src\application\ports\ValidationAgentPort.ts(NOUVEAU)
- src\domain\entities\ValidationReport.ts(NOUVEAU)
- .claude\agents\spec-requirements-validator.md

Créer McpValidationAdapter qui implémente l'interface ValidationAgentPort. Cet adaptateur simulera le comportement des agents de validation du dossier `.claude/agents/` en implémentant la logique de validation décrite dans `spec-requirements-validator.md` et `spec-design-validator.md`. Il validera la structure du document, la qualité du contenu et l'alignement avec les modèles, en retournant des entités ValidationReport avec le statut et les retours appropriés.

### src\infrastructure\adapters\BasicCodeGenerator.ts(NOUVEAU)

Références : 

- src\application\ports\CodeGeneratorPort.ts(NOUVEAU)
- src\domain\entities\CodeSkeleton.ts(NOUVEAU)
- .claude\templates\tech-template.md
- .claude\templates\structure-template.md

Créer BasicCodeGenerator qui implémente l'interface CodeGeneratorPort. Cet adaptateur générera des squelettes de code basiques à partir des documents de conception. Il analysera le contenu de la conception pour identifier les composants, modèles et services, puis générera la structure de fichiers appropriée et des templates de classe/fonction basiques. Le générateur utilisera `tech-template.md` et `structure-template.md` pour comprendre la stack technologique et les schémas d'organisation du projet.

### src\infrastructure\adapters\index.ts(NOUVEAU)

Références : 

- src\infrastructure\adapters\FileSystemTemplateProvider.ts(NOUVEAU)
- src\infrastructure\adapters\McpValidationAdapter.ts(NOUVEAU)
- src\infrastructure\adapters\BasicCodeGenerator.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour les adaptateurs d'infrastructure afin d'exporter FileSystemTemplateProvider, McpValidationAdapter et BasicCodeGenerator.

### src\infrastructure\index.ts(NOUVEAU)

Références : 

- src\infrastructure\adapters\index.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la couche infrastructure afin d'exposer les adaptateurs et implémentations.

### src\presentation(NOUVEAU)

Créer le dossier présentation pour contenir les points d'entrée et les adaptateurs d'interface utilisateur.

### src\presentation\mcp(NOUVEAU)

Créer le dossier MCP présentation pour contenir la logique de présentation spécifique à MCP.

### src\presentation\mcp\McpPromptHandlers.ts(NOUVEAU)

Références : 

- src\server.ts(MODIFIER)
- src\application\use-cases\index.ts(NOUVEAU)

Créer McpPromptHandlers qui contient les fonctions de gestion des prompts MCP. Cela inclura les gestionnaires pour les prompts existants (generate-requirements, generate-design-from-requirements, generate-code-from-design) et les deux nouveaux prompts (spec-create-full, spec-validate). Chaque gestionnaire instanciera le cas d'utilisation approprié, l'exécutera et formatra la réponse sous forme de messages MCP chat. Les gestionnaires conserveront le même format de réponse que l'implémentation actuelle dans `src/server.ts`.

### src\presentation\mcp\McpServerFactory.ts(NOUVEAU)

Références : 

- src\server.ts(MODIFIER)
- src\infrastructure\adapters\index.ts(NOUVEAU)
- src\application\use-cases\index.ts(NOUVEAU)
- src\presentation\mcp\McpPromptHandlers.ts(NOUVEAU)

Créer McpServerFactory qui remplace la fonction `createServer` de `src/server.ts`. Cette factory : 1) Instancie tous les adaptateurs d'infrastructure (FileSystemTemplateProvider, McpValidationAdapter, BasicCodeGenerator), 2) Crée les instances de cas d'utilisation avec les dépendances injectées, 3) Enregistre les 5 prompts MCP (3 existants + 2 nouveaux) auprès du serveur MCP, 4) Relie les gestionnaires de prompts aux cas d'utilisation appropriés. La factory doit conserver exactement la même signature publique `(): McpServer` pour garantir la compatibilité.

### src\presentation\mcp\index.ts(NOUVEAU)

Références : 

- src\presentation\mcp\McpServerFactory.ts(NOUVEAU)
- src\presentation\mcp\McpPromptHandlers.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la présentation MCP afin d'exporter McpServerFactory et McpPromptHandlers.

### src\presentation\index.ts(NOUVEAU)

Références : 

- src\presentation\mcp\index.ts(NOUVEAU)

Créer un fichier d'export "barrel" pour la couche présentation afin d'exposer la factory du serveur MCP.

### src\server.ts(MODIFIER)

Références : 

- src\presentation\mcp\McpServerFactory.ts(NOUVEAU)
- src\index.ts
- src\streamableHttp.ts

Refactoriser le fichier server.ts existant pour utiliser la nouvelle architecture hexagonale. Remplacer l'implémentation actuelle de la fonction `createServer` pour déléguer à la nouvelle `McpServerFactory` de la couche présentation. Retirer toute la logique métier et le code d'enregistrement des prompts qui ont été déplacés dans les couches présentation et application. Ne garder qu'une délégation simple : `export function createServer(): McpServer { return McpServerFactory.create(); }`. Maintenir la même signature d'export pour garantir la compatibilité avec `src/index.ts` et `src/streamableHttp.ts`.

### .claude\commands\spec-create-full.md(NOUVEAU)

Références : 

- .claude\commands\spec-create.md
- .claude\templates\requirements-template.md
- .claude\templates\design-template.md

Créer la spécification de commande pour `/spec-create-full` en suivant le même format que les commandes existantes dans `.claude/commands/`. Documenter l'utilisation de la commande, la philosophie et le workflow. Préciser que cette commande génère requirements.md, design.md et les squelettes de code en une seule opération. Inclure le schéma des arguments (nom de la fonctionnalité et description), les sorties attendues et l'intégration avec les modèles existants. Référencer les modèles du dossier `.claude/templates/` qui seront utilisés comme base pour la génération.

### .claude\commands\spec-validate.md(NOUVEAU)

Références : 

- .claude\commands\spec-status.md
- .claude\agents\spec-requirements-validator.md
- .claude\agents\spec-design-validator.md

Créer la spécification de commande pour `/spec-validate` en suivant le même format que les commandes existantes. Documenter l'utilisation de la commande pour valider la cohérence entre les spécifications et le code. Préciser que cette commande utilise les agents de validation existants du dossier `.claude/agents/` pour vérifier la qualité de requirements.md et design.md, puis effectue une validation croisée avec le code existant. Inclure les critères de validation attendus, le format de sortie (PASS/BESOIN_AMÉLIORATION/PROBLÈMES_MAJORS) et les points d'intégration avec les agents de validation.