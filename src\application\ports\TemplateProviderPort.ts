/**
 * Port (interface) pour charger les fichiers modèles.
 * Cette interface définit le contrat pour charger les modèles depuis le dossier .claude/templates/
 */
export interface TemplateProviderPort {
  /**
   * Charge un modèle par son nom
   * @param templateName Le nom du modèle (ex: "requirements-template", "design-template")
   * @returns Le contenu du modèle
   */
  getTemplate(templateName: string): Promise<string>;
  
  /**
   * Vérifie si un modèle existe
   * @param templateName Le nom du modèle
   * @returns True si le modèle existe, false sinon
   */
  templateExists(templateName: string): Promise<boolean>;
}
