# Structure du projet

## Organisation des répertoires

```
[Définissez la structure des répertoires de votre projet. Exemples ci-dessous - adaptez selon votre type de projet]

Exemple pour une bibliothèque/package :
racine-du-projet/
├── src/                    # Code source
├── tests/                  # Fichiers de test  
├── docs/                   # Documentation
├── examples/               # Exemples d'utilisation
└── [build/dist/out]        # Fichiers générés

Exemple pour une application :
racine-du-projet/
├── [src/app/lib]           # Code source principal
├── [assets/resources]      # Ressources statiques
├── [config/settings]       # Configuration
├── [scripts/tools]         # Scripts d'automatisation/outils
└── [tests/spec]            # Fichiers de test

Schémas courants :
- Regrouper par fonctionnalité/module
- Regrouper par couche (UI, logique métier, données)
- Regrouper par type (modèles, contrôleurs, vues)
- Structure plate pour les projets simples
```

## Conventions de nommage

### Fichiers
- **Composants/Modules** : [ex : `PascalCase`, `snake_case`, `kebab-case`]
- **Services/Handlers** : [ex : `UserService`, `user_service`, `user-service`]
- **Utilitaires/Aides** : [ex : `dateUtils`, `date_utils`, `date-utils`]
- **Tests** : [ex : `[nom_fichier]_test`, `[nom_fichier].test`, `[nom_fichier]Test`]

### Code
- **Classes/Types** : [ex : `PascalCase`, `CamelCase`, `snake_case`]
- **Fonctions/Méthodes** : [ex : `camelCase`, `snake_case`, `PascalCase`]
- **Constantes** : [ex : `UPPER_SNAKE_CASE`, `SCREAMING_CASE`, `PascalCase`]
- **Variables** : [ex : `camelCase`, `snake_case`, `lowercase`]

## Schémas d'importation

### Ordre des imports
1. Dépendances externes
2. Modules internes
3. Imports relatifs
4. Imports de styles

### Organisation des modules/packages
```
[Décrivez les schémas d'importation/inclusion de votre projet]
Exemples :
- Imports absolus depuis la racine du projet
- Imports relatifs au sein des modules
- Organisation par package/namespace
- Approche de gestion des dépendances
```

## Schémas de structure du code

[Définissez les schémas courants pour organiser le code dans les fichiers. Ci-dessous des exemples - choisissez ce qui convient à votre projet]

### Organisation des modules/classes
```
Exemples de schémas :
1. Imports/inclusions/dépendances
2. Constantes et configuration
3. Définitions de types/interfaces
4. Implémentation principale
5. Fonctions utilitaires/aides
6. Exports/API publique
```

### Organisation des fonctions/méthodes
```
Exemples de schémas :
- Validation des entrées en premier
- Logique principale au centre
- Gestion des erreurs tout au long
- Points de retour clairs
```

### Principes d'organisation des fichiers
```
Choisissez ce qui fonctionne pour votre projet :
- Une classe/module par fichier
- Fonctionnalités liées regroupées
- API publique en haut/en bas
- Détails d'implémentation cachés
```

## Principes d'organisation du code

1. **Responsabilité unique** : Chaque fichier doit avoir un objectif clair
2. **Modularité** : Le code doit être organisé en modules réutilisables
3. **Testabilité** : Structurez le code pour faciliter les tests
4. **Cohérence** : Suivez les schémas établis dans la base de code

## Limites des modules
[Définissez comment les différentes parties de votre projet interagissent et maintiennent la séparation des responsabilités]

Exemples de schémas de limites :
- **Noyau vs Plugins** : Fonctionnalité centrale vs plugins extensibles
- **API publique vs Interne** : Ce qui est exposé vs détails d'implémentation  
- **Spécifique à la plateforme vs Multi-plateforme** : Isolation du code spécifique à l'OS
- **Stable vs Expérimental** : Code de production vs fonctionnalités expérimentales
- **Direction des dépendances** : Quels modules peuvent dépendre de quels autres

## Directives sur la taille du code
[Définissez les directives de votre projet pour la taille des fichiers et des fonctions]

Directives suggérées :
- **Taille des fichiers** : [Définir le nombre maximal de lignes par fichier]
- **Taille des fonctions/méthodes** : [Définir le nombre maximal de lignes par fonction]
- **Complexité des classes/modules** : [Définir les limites de complexité]
- **Profondeur d'imbrication** : [Niveaux d'imbrication maximum]

## Structure du tableau de bord/surveillance (si applicable)
[Comment les composants de tableau de bord ou de monitoring sont organisés]

### Exemple de structure :
```
src/
└── dashboard/          # Sous-système tableau de bord autonome
    ├── server/        # Composants serveur backend
    ├── client/        # Ressources frontend
    ├── shared/        # Types/utilitaires partagés
    └── public/        # Ressources statiques
```

### Séparation des responsabilités
- Tableau de bord isolé de la logique métier principale
- Point d'entrée CLI dédié pour un fonctionnement indépendant
- Dépendances minimales sur l'application principale
- Peut être désactivé sans affecter la fonctionnalité principale

## Standards de documentation
- Toutes les APIs publiques doivent être documentées
- La logique complexe doit inclure des commentaires en ligne
- Fichiers README pour les principaux modules
- Suivre les conventions de documentation spécifiques au langage
