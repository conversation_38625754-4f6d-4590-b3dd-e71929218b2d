import { ChatMessage } from '../../domain/entities/ChatMessage.js';
import { PromptBuilderPort } from '../ports/PromptBuilderPort.js';

/**
 * Cas d'utilisation pour générer des documents d'exigences.
 * Encapsule la logique métier pour le prompt generate-requirements.
 */
export class GenerateRequirementsUseCase {
  constructor(private promptBuilder: PromptBuilderPort) {}

  /**
   * Exécute le cas d'utilisation pour générer des exigences
   * @param requirements Les exigences de haut niveau de l'application
   * @returns La réponse formatée pour MCP
   */
  execute(requirements: string): { messages: any[] } {
    const chatMessages: ChatMessage[] = [{
      role: "user",
      content: {
        type: "text",
        text: `Based on below requirements, generate requirements.md using EARS format in 'specs' folder:\n\n${requirements}`
      }
    }];

    return this.promptBuilder.buildPromptResponse(chatMessages);
  }
}
