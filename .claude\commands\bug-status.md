# Commande Statut des Bugs

Affiche le statut actuel de toutes les corrections de bugs ou d'une correction spécifique.

## Utilisation
```
/bug-status [nom-du-bug]
```

## Instructions
Affiche le statut actuel des workflows de correction de bugs.

1. **Si aucun nom-du-bug n'est fourni :**
   - Lister tous les bugs dans le répertoire `.sdd/bugs/`
   - Afficher la phase actuelle pour chaque bug
   - Afficher le statut d'avancement

2. **Si un nom-du-bug est fourni :**
   - Afficher le statut détaillé pour ce bug
   - Afficher la phase actuelle du workflow
   - Afficher les phases complétées vs en attente
   - Lister les prochaines actions recommandées

3. **Informations de Statut :**
   - Signalement : [Terminé/En cours/En attente]
   - Analyse : [Terminé/En cours/En attente]
   - Correction : [Terminé/En cours/En attente]
   - Vérification : [Terminé/En cours/En attente]

4. **Format de sortie :**
   ```
   Bug : login-timeout
   Phase : Implémentation de la correction
   Progression : Signalement ✅ | Analyse ✅ | Correction 🔄 | Vérification ⏳
   Statut : Implémentation de la correction pour le problème de timeout de session
   Suivant : Terminer l'implémentation et vérifier que la correction fonctionne
   ```

## Phases de Correction de Bug
- **Signalement** : Description du bug et évaluation de l'impact
- **Analyse** : Recherche de la cause racine et planification de la solution
- **Correction** : Implémentation de la solution planifiée
- **Vérification** : Test et confirmation de la résolution
- **Terminé** : Bug entièrement résolu et vérifié
