# Commande Spec Create Full

Créez un package de spécification complet en une seule opération : requirements.md + design.md + squelettes de code.

## Utilisation
```
/spec-create-full <nom-fonctionnalité> <description>
```

## Philosophie du workflow

Cette commande implémente une approche accélérée du développement piloté par les spécifications, générant automatiquement l'ensemble complet des documents et structures de code nécessaires pour démarrer rapidement l'implémentation d'une nouvelle fonctionnalité.

### Principes clés
- **Génération automatisée** : Crée automatiquement requirements.md, design.md et les squelettes de code
- **Basé sur les modèles** : Utilise les modèles existants du dossier `.claude/templates/`
- **Structure cohérente** : Garantit la cohérence entre les documents générés
- **Prêt pour l'implémentation** : Fournit une base solide pour commencer le développement

## Workflow automatisé

**SÉQUENCE AUTOMATIQUE** : Exigences → Conception → Squelettes de code → Structure projet

### Phase 1 : Génération des exigences
- Utilise le modèle `requirements-template.md`
- Génère des user stories basées sur la description fournie
- Applique le format EARS pour les critères d'acceptation
- Intègre les meilleures pratiques de spécification

### Phase 2 : Génération de la conception
- Utilise le modèle `design-template.md`
- Crée une architecture cohérente avec les exigences
- Définit les composants et interfaces nécessaires
- Inclut des diagrammes Mermaid pour la visualisation

### Phase 3 : Génération des squelettes de code
- Analyse la conception pour identifier les composants
- Génère des squelettes TypeScript/JavaScript
- Crée la structure de fichiers appropriée
- Fournit des templates de classes et fonctions

### Phase 4 : Structure du projet
- Crée l'arborescence de dossiers recommandée
- Organise les fichiers selon les conventions
- Prépare l'environnement de développement

## Arguments

### nom-fonctionnalité (requis)
- **Type** : String
- **Format** : kebab-case recommandé
- **Description** : Nom unique de la fonctionnalité à créer
- **Exemple** : `user-authentication`, `payment-processing`

### description (requis)
- **Type** : String
- **Description** : Description détaillée de la fonctionnalité et de ses exigences
- **Exemple** : "Système d'authentification permettant aux utilisateurs de s'inscrire, se connecter et gérer leur profil avec validation email et récupération de mot de passe"

## Sorties générées

### Documents de spécification
1. **requirements.md** - Document d'exigences complet
2. **design.md** - Document de conception détaillé

### Structure de code
1. **Squelettes de fichiers** - Classes, interfaces et fonctions de base
2. **Structure de dossiers** - Organisation recommandée du projet
3. **Points d'intégration** - Interfaces avec le code existant

### Formats de sortie
- **Markdown structuré** pour les documents
- **TypeScript/JavaScript** pour les squelettes de code
- **Arborescence de dossiers** pour l'organisation

## Intégration avec les modèles

### Modèles utilisés
- `.claude/templates/requirements-template.md` - Structure des exigences
- `.claude/templates/design-template.md` - Structure de conception
- `.claude/templates/tech-template.md` - Standards techniques (si disponible)
- `.claude/templates/structure-template.md` - Organisation projet (si disponible)

### Variables de substitution
Les modèles supportent les variables suivantes :
- `{{featureName}}` - Nom de la fonctionnalité
- `{{description}}` - Description fournie
- `{{date}}` - Date de création
- `{{requirements}}` - Contenu des exigences (pour design)

## Avantages

### Rapidité
- Génération complète en une seule commande
- Pas d'attente entre les phases
- Démarrage immédiat du développement

### Cohérence
- Alignement automatique entre les documents
- Respect des modèles et standards
- Structure uniforme

### Complétude
- Tous les éléments nécessaires générés
- Rien d'oublié dans le processus
- Base solide pour l'implémentation

## Exemple d'utilisation

```
/spec-create-full user-profile "Système de gestion de profil utilisateur permettant la modification des informations personnelles, la gestion des préférences et l'upload d'avatar avec validation et sauvegarde automatique"
```

### Résultat attendu
- `requirements.md` avec user stories et critères d'acceptation
- `design.md` avec architecture et composants
- Squelettes de code pour UserProfile, ProfileService, ProfileComponent
- Structure de dossiers pour features/user-profile/

## Différences avec /spec-create

| Aspect | /spec-create | /spec-create-full |
|--------|--------------|-------------------|
| **Approche** | Séquentielle avec approbations | Automatisée complète |
| **Interaction** | Requiert validation à chaque étape | Génération en une fois |
| **Flexibilité** | Haute - modifications possibles | Moyenne - post-génération |
| **Rapidité** | Lente - processus itératif | Rapide - résultat immédiat |
| **Contrôle** | Total sur chaque phase | Contrôle global final |

## Cas d'usage recommandés

### Idéal pour
- **Prototypage rapide** - Besoin de démarrer rapidement
- **Fonctionnalités standard** - Patterns bien établis
- **Exploration d'idées** - Test de concepts
- **Développement solo** - Pas besoin de validation externe

### Moins adapté pour
- **Fonctionnalités complexes** - Nécessitant analyse approfondie
- **Projets critiques** - Requérant validation métier
- **Équipes multiples** - Besoin de consensus
- **Contraintes spécifiques** - Exigences non-standard

## Post-génération

Après la génération complète :

1. **Révision recommandée** - Vérifiez la cohérence des documents
2. **Adaptation si nécessaire** - Modifiez selon les besoins spécifiques
3. **Validation optionnelle** - Utilisez `/spec-validate` pour vérifier
4. **Démarrage implémentation** - Commencez le développement avec les squelettes

## Commandes complémentaires

- `/spec-validate` - Valider la cohérence des documents générés
- `/spec-status` - Suivre l'avancement de l'implémentation
- `/spec-execute` - Exécuter des tâches spécifiques d'implémentation
