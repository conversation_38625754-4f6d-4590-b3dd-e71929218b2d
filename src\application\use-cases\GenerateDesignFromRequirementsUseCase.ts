import { ChatMessage } from '../../domain/entities/ChatMessage.js';
import { PromptBuilderPort } from '../ports/PromptBuilderPort.js';

/**
 * Cas d'utilisation pour générer des documents de conception à partir des exigences.
 * Encapsule la logique métier pour le prompt generate-design-from-requirements.
 */
export class GenerateDesignFromRequirementsUseCase {
  constructor(private promptBuilder: PromptBuilderPort) {}

  /**
   * Exécute le cas d'utilisation pour générer une conception
   * @returns La réponse formatée pour MCP
   */
  execute(): { messages: any[] } {
    const chatMessages: ChatMessage[] = [{
      role: "user",
      content: {
        type: "text",
        text: `Based on specs/requirements.md, generate specs/design.md`
      }
    }];

    return this.promptBuilder.buildPromptResponse(chatMessages);
  }
}
